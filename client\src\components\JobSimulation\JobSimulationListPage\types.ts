export interface IListJobPageJobSimulation {
  id: string;
  jobSimulationId: string;
  name: string;
  description: string;
  logo: string;
  companyName: string;
  participants: number;
  agentId?: string;
  isLocal?: boolean;
  progress?: {
    status: string;
    intakeId: string;
    email: string;
  };
}

export interface IListJobPageAgent {
  id: string;
  name: string;
  avatar: {
    filepath: string;
  };
}