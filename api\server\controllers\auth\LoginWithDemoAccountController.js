const { setAuthTokens } = require('~/server/services/AuthService');
const { logger } = require('~/config');
const BillionService = require('~/server/services/Billion/BillionService');
const {
  findUser
} = require('~/models/userMethods');

const DEMO_ACCOUNT = '<EMAIL>';

const loginWithDemoAccountController = async (req, res) => {
  try {
    const user = await findUser({ email: DEMO_ACCOUNT });

    if (!user) {
      return res.status(400).json({ message: 'User not found' });
    }

    const { password: _p, totpSecret: _t, __v, secretCode: _s, ...safeUser } = user;
    safeUser.id = user._id.toString();

    const token = await setAuthTokens(user._id.toString(), res);

    return res.status(200).send({ token, user: safeUser });
  } catch (err) {
    logger.error('[loginWithDemoAccountController]', err);
    return res.status(500).json({ message: 'Something went wrong' });
  }
};


module.exports = {
  loginWithDemoAccountController,
};
