import { useEffect } from 'react';
import { OGDialog, OGDialogContent } from '~/components';
import { useUpdateJobSimulationProgress } from '~/data-provider/JobSimulation/mutations';

const CertificationDialog = (params: {
  intakeId: string;
  email: string;
  isOpen: boolean;
  jobSimulationId: string;
  setIsOpen: (isOpen?: boolean) => void;
}) => {
  const updateJobSimulationProgress = useUpdateJobSimulationProgress();
  const { isOpen, setIsOpen, intakeId, email, jobSimulationId } = params;
  const url =
    intakeId && email
      ? `https://uat.internship.guru/en/public/reference-letter?programId=${intakeId}&secret=${email}`
      : '';

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // console.log('Received message ::: ', event.data);
      const { type, data } = event.data || {};
      if (type === 'BILLION_CERT') {
        const actionType = data?.type;
        if (actionType === 'CLAIMED') {
          updateJobSimulationProgress.mutateAsync({
            jobSimulationId,
            claimedCertification: true,
          });
        } else if (actionType === 'DOWNLOADED') {
          updateJobSimulationProgress.mutateAsync({
            jobSimulationId,
            downloadedCertification: true,
            claimedCertification: true,
          });
        }
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);
  return (
    <OGDialog open={isOpen} onOpenChange={setIsOpen}>
      <OGDialogContent className="h-screen max-w-6xl">
        {/* <OGDialogHeader>
          <OGDialogTitle>Certification</OGDialogTitle>
        </OGDialogHeader> */}

        <div className="flex flex-col">
          {url && (
            <iframe
              src={url}
              className="h-full w-full border-0"
              allow="camera; microphone; display-capture; clipboard-read; clipboard-write; fullscreen"
            />
          )}
        </div>
      </OGDialogContent>
    </OGDialog>
  );
};

export default CertificationDialog;
