import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Button } from '~/components';
import FeedbackDialog from '~/components/JobSimulation/FeedbackDialog';
import { CheckMark } from '~/components/svg';
import { cn } from '~/utils';
import { IListJobPageAgent, IListJobPageJobSimulation } from './types';

// Job card component
const JobCard = ({
  job,
  agent,
  onViewDetails,
  onViewCertification,
}: {
  job: IListJobPageJobSimulation;
  agent?: IListJobPageAgent;
  onViewDetails: (job: IListJobPageJobSimulation) => void;
  onViewCertification: (job: IListJobPageJobSimulation) => void;
}) => {
  return (
    <motion.div
      className="animate-fadeIn relative flex w-full flex-col rounded-2xl border border-border-light bg-white p-4 shadow-sm transition-all duration-200 hover:shadow-md dark:bg-gray-700"
      whileHover={{ scale: 1.03 }}
      transition={{ duration: 0.2, ease: 'easeOut' }}
    >
      <div className="relative mb-4 flex h-52 items-center justify-center rounded-2xl bg-[#E1E7F4] dark:bg-black">
        <img
          src="/assets/job-logo.png"
          alt={`logo`}
          className="max-h-[55%] max-w-[80%] object-contain"
        />
        {/* Add background black for text, and always use text-white */}
        {/* <h3 className="absolute bottom-4 left-4 text-2xl font-semibold text-black dark:text-white">
          {job.name}
        </h3> */}
      </div>

      <h4 className="text-xl font-semibold text-black dark:text-white">{job.name}</h4>
      <p className="mb-2 text-sm text-gray-600 dark:text-gray-300">
        at <span className="font-semibold">{job.companyName}</span>
      </p>

      <p className="mb-3 line-clamp-3 h-[4.5em] text-sm text-gray-600 dark:text-gray-300">
        {job.description}
      </p>

      {/* <div className="mb-3 flex items-center text-sm text-gray-500 dark:text-gray-400">
        <span>{job.participants.toLocaleString()} participants</span>
      </div> */}

      {/* {agent && (
        <div className="mb-4 flex items-center gap-2">
          <img
            src={agent.avatar.filepath}
            alt={agent.name}
            className="h-8 w-8 rounded-full object-cover"
          />
          <span className="text-sm font-semibold">{agent.name}</span>
        </div>
      )} */}

      <div className="mt-auto flex flex-row gap-2">
        {job.progress?.status === 'completed' ? (
          <>
            {!job.isLocal && (
              <>
                <span className="flex flex-1 items-center justify-start gap-2 text-[#00C224]">
                  <Button
                    onClick={() => onViewCertification(job)}
                    variant="outline"
                    className="h-auto w-auto rounded-2xl border-job-primary px-3 py-3"
                  >
                    View certification
                  </Button>
                </span>

                <FeedbackDialog jobId={job?.jobSimulationId} />
              </>
            )}

            <span className="absolute right-3 top-3 flex items-center justify-start gap-2 rounded-full bg-gray-500 px-2 py-1 text-[#00C224] dark:bg-white">
              <CheckMark />
              <span>Completed</span>
            </span>
          </>
        ) : (
          <>
            <Button
              onClick={() => onViewDetails(job)}
              variant="outline"
              className="h-auto w-full rounded-2xl px-3 py-3"
            >
              View details
            </Button>

            <Link
              to={`/job-simulation/${job.jobSimulationId}`}
              className={cn(
                'flex w-full items-center justify-center rounded-2xl bg-job-primary px-3 py-3 text-center text-sm font-medium text-white',
                'hover:bg-job-primary/90 focus:outline-none focus:ring-2 focus:ring-job-primary focus:ring-offset-2',
              )}
            >
              {job.progress?.status === 'active' ? 'Resume' : 'Give it a try'}
            </Link>
          </>
        )}
      </div>
    </motion.div>
  );
};

export default JobCard;
