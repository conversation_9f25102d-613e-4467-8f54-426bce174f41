const mongoose = require('mongoose');
const { jobSimulationProgressSchema } = require('@librechat/data-schemas');
// const { logger } = require('~/config');

const JobSimulationProgress = mongoose.model('jobSimulationProgress', jobSimulationProgressSchema);

const getOrCreate = async function (params) {
  try {
    const { jobSimulationId, email, userId, jobStatus } = params;
    // let progress = await JobSimulationProgress.findOne({ jobSimulationId, email, status: 'active' }).lean();
    // TODO: Maybe in the future, we allow user to reset the progress. If so, we need to query the status
    let progress = await JobSimulationProgress.findOne({ jobSimulationId, email }).lean();
    if (progress) return progress;
    progress = await JobSimulationProgress.create({
      jobSimulationId,
      email,
      userId,
      sessionId: new mongoose.Types.ObjectId().toString(),
      status: 'active',
      jobStatus,
    });
    return progress;
  } catch (error) {
    throw new Error(`Failed to retrieve job simulation progress: ${error.message}`);
  }
};

const getProgress = async function (params) {
  try {
    const { jobSimulationId, email } = params;
    return await JobSimulationProgress.findOne({ jobSimulationId, email }).lean();
  } catch (error) {
    throw new Error(`Failed to retrieve job simulation progress: ${error.message}`);
  }
};

const getProgresses = async function (params) {
  try {
    const { jobSimulationIds, email } = params;
    return await JobSimulationProgress.find(
      { jobSimulationId: { $in: jobSimulationIds }, email },
      { email: 1, jobSimulationId: 1, status: 1, intakeId: 1 },
    );
  } catch (error) {
    throw new Error(`Failed to retrieve job simulation progress: ${error.message}`);
  }
};

const saveConversationId = async function (params) {
  try {
    const { jobSimulationId, email, conversationId } = params;
    return await JobSimulationProgress.findOneAndUpdate(
      { jobSimulationId, email },
      { $set: { conversationId } },
    ).lean();
  } catch (error) {
    throw new Error(`Failed to retrieve job simulation progress: ${error.message}`);
  }
};

const saveIntakeId = async function (params) {
  try {
    const { jobSimulationId, email, intakeId } = params;
    return await JobSimulationProgress.findOneAndUpdate(
      { jobSimulationId, email },
      { $set: { intakeId } },
    ).lean();
  } catch (error) {
    throw new Error(`Failed to save progress intakeId: ${error.message}`);
  }
};

const update = async function (params) {
  try {
    const { jobSimulationId, email, data } = params;
    let updatedProgress = await JobSimulationProgress.findOneAndUpdate(
      {
        jobSimulationId,
        email,
        // TODO: what is this logic for ?? Maybe: If we want to mark this progress as completed, the current status must be 'active' (?)
        ...(data.status || data.completedAt ? { status: 'active' } : {}),
      },
      { $set: { ...(data || {}) } },
      { new: true },
    ).lean();

    if (data.completedAt) {
      updatedProgress = await JobSimulationProgress.findOneAndUpdate(
        {
          jobSimulationId,
          email,
        },
        {
          $set: {
            completionMins: Math.round(
              (new Date(updatedProgress.completedAt).getTime() -
                new Date(updatedProgress.createdAt).getTime()) /
                (1000 * 60),
            ),
          },
        },
        { new: true },
      ).lean();
    }
  } catch (error) {
    throw new Error(`Failed to save progress intakeId: ${error.message}`);
  }
};

module.exports = {
  model: JobSimulationProgress,
  getProgress,
  getProgresses,
  getOrCreate,
  saveConversationId,
  saveIntakeId,
  update,
};
