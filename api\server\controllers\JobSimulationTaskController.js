const JobSimulationService = require('~/server/services/JobSimulation/JobSimulationService');
const JobSimulationInteractionService = require('~/server/services/JobSimulation/JobSimulationInteractionService');
const BillionService = require('~/server/services/Billion/BillionService');
const { getRandomFeedback } = require('~/server/utils/jobSimulation/taskSubmissionFeedback');
const OpenAI = require('openai');
const dedent = require('dedent');
// const { logger } = require('~/config');

const systemPromptVerifyTask = (task) => {
  return dedent`You are an AI Task Verifier. Your job is to evaluate a user's task submission based on the task description.

${task.prompt || ""}

You must return a JSON object in the following format:
{
  "status": "passed" | "failed",
  "feedback": "- bullet point 1newline- bullet point 2newline...",
  "scores": integer (minimum 5, maximum 10, if passed then score >= 8, otherwise < 8)
}

Important:
- Use 'newline' keyword to separate bullet points.
- Do NOT use markdown formatting (like \`\`\`json).
- Only return raw JSON.

Always give feedback regardless of pass/fail. Feedback should be concise and helpful.`;
}

const userPromptVerifyTask = (submissionContent, taskDescription) => {
  return dedent`
${taskDescription ? `Task description:
${taskDescription}`
      : ''}

User submission:
${submissionContent}

Evaluate and return the result in the required JSON format.
`;
}

const verifyTaskAI = async (task, submissionContent) => {
  const openaiClient = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

  const messages = [
    { role: "system", content: systemPromptVerifyTask(task) },
    {
      role: "user",
      content: userPromptVerifyTask(submissionContent),
    },
  ];
  const responseFromOpenAI = await openaiClient.chat.completions.create({
    model: "gpt-4o-mini",
    messages,
  });

  const responseString = responseFromOpenAI.choices?.[0]?.message?.content || "";

  try {
    const result = JSON.parse(responseString || "");
    // Validate format
    if (
      typeof result.status === "string" &&
      typeof result.feedback === "string" &&
      typeof result.scores === "number"
    ) {
      result.feedback = result.feedback.replace(/newline/g, '\n');
      return result;
    } else {
      throw new Error("Invalid format");
    }
  } catch (err) {
    throw new Error("Failed to parse response: " + responseString);
  }

}

const JobSimulationTaskController = {
  async getAvailableTask(req, res) {
    try {
      JobSimulationService.updateProgressTasks(req.params.jobSimulationId, req.user.email);
      const jobSimulation = await JobSimulationService.getAdminJobSimulation(req.params.jobSimulationId);
      if (!jobSimulation) {
        throw new Error('Job simulation not found');
      }
      let tasks = [];
      if (jobSimulation.isLocal) {
        const jobSimulationProgress = await JobSimulationService.getProgress(req.params.jobSimulationId, req.user.email);
        const mapProgressTaskById = (jobSimulationProgress.tasks || []).reduce((acc, curr) => {
          if (!curr.status) {
            if (curr.completedAt) {
              curr.status = 'passed';
            } else {
              curr.status = 'todo';
            }
          }
          acc[curr.id] = curr;
          return acc;
        }, {});

        tasks = jobSimulation.tasks.map(task => ({
          taskId: task.id,
          taskName: task.title,
          taskDescription: task.description,
          status: mapProgressTaskById[task.id]?.status || 'todo',
          ...(mapProgressTaskById[task.id]?.completedAt ? { completedAt: mapProgressTaskById[task.id].completedAt } : {}),
        }));
      } else {
        tasks = await BillionService.getTasks({ email: req.user.email, billionIntakeId: jobSimulation.billionIntakeId });
      }
      if (tasks?.length) {
        // Save tasks to user interaction
        await JobSimulationInteractionService.saveUserInteraction({
          jobSimulationId: req.params.jobSimulationId,
          jobSimulationEmail: req.user.email,
          interaction: {
            type: 'update-tasks',
            tasks,
          },
        });
      }
      const availableTask = (tasks || []).find(task => task.status !== 'passed') || {};

      res.json({
        jobSimulation,
        task: availableTask,
        user: req.user,
      });
    } catch (error) {
      console.error('=== ERROR RETRIEVING TASKS ===', error);
      res.status(500).json({ error: 'Failed to get tasks' });
    }
  },

  async submitTask(req, res) {
    try {
      const jobSimulation = await JobSimulationService.getAdminJobSimulation(req.params.jobSimulationId);
      if (!jobSimulation) {
        throw new Error('Job simulation not found');
      }
      // Save progress email
      const progress = await JobSimulationService.updateProgressEmailReplies(req.params.jobSimulationId, req.user.email, req.body.emailId, {
        id: req.body.replyId,
        isUser: true,
        datetime: Date.now(),
        content: req.body.content,
      });
      // { status: string, feedback: string }
      let resultTaskSubmission = null;
      if (jobSimulation.isLocal) {
        const jobSimulationTask = jobSimulation.tasks.find(task => task.id === req.body.taskId);
        if (!jobSimulationTask) {
          throw new Error('Task not found');
        }
        resultTaskSubmission = await verifyTaskAI(jobSimulationTask, req.body.content);
      } else {
        resultTaskSubmission = await BillionService.submitTask({ email: req.user.email, taskId: req.body.taskId, content: req.body.content, billionIntakeId: jobSimulation.billionIntakeId });
      }
      // Save user interaction `tasks`

      let feedback = '';
      if (Array.isArray(resultTaskSubmission?.feedback)) {
        feedback = responseFeedback.join('\n');
      } else if (resultTaskSubmission.feedback) {
        feedback = String(resultTaskSubmission.feedback);
      }

      feedback = getRandomFeedback(resultTaskSubmission.status, feedback);

      if (resultTaskSubmission && resultTaskSubmission.status === 'passed') {
        await JobSimulationInteractionService.saveUserInteraction({
          jobSimulationId: req.params.jobSimulationId,
          jobSimulationEmail: req.user.email,
          interaction: {
            type: 'update-task',
            task: {
              taskId: req.body.taskId,
              status: 'passed',
            },
          },
        });
      }
      resultTaskSubmission.feedback = feedback;
      const email = progress.emails.find(email => email.id === req.body.emailId);
      if (email) {
        await JobSimulationService.updateProgressEmailReplies(req.params.jobSimulationId, req.user.email, req.body.emailId, {
          id: `reply-${(email.replies?.length || 1) + 1}:email-task:${req.body.taskId}`,
          isUser: false,
          datetime: Date.now(),
          content: feedback,
        });
      }
      try {
        if (jobSimulation.isLocal) {
          await JobSimulationService.updateProgressLocalTasks(req.params.jobSimulationId, req.user.email, req.body.taskId, {
            content: req.body.content,
            feedback,
            status: resultTaskSubmission.status,
            scores: resultTaskSubmission.scores,
          });
        } else {
          await JobSimulationService.updateProgressTasks(req.params.jobSimulationId, req.user.email);
        }
      } catch (error) {
      }
      res.json(resultTaskSubmission);
    } catch (error) {
      console.error('=== ERROR RETRIEVING TASKS ===', error);
      res.status(500).json({ error: 'Failed to submit tasks' });
    }
  },

  // async updateProgressTasks(req, res) {
  //   try {
  //     await JobSimulationService.updateProgressTasks(req.params.jobSimulationId, req.user.email);
  //     res.json({
  //       jobSimulationId: req.params.jobSimulationId,
  //     });
  //   } catch (error) {
  //     console.error('=== ERROR UPDATING PROGRESS TASKS ===', error);
  //     res.status(500).json({ error: 'Failed to get tasks' });
  //   }
  // },
};

module.exports = JobSimulationTaskController;