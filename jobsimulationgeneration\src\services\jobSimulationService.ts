import { PipelineStage } from 'mongoose';
import JobSimulationModel from '../models/JobSimulation';

const findOne = async (filter: Record<string, any>) => {
    return await JobSimulationModel.findOne(filter);
};

const findOneAndUpdate = async (filter: Record<string, any>, update: Record<string, any>, options?: Record<string, any>) => {
    return await JobSimulationModel.findOneAndUpdate(filter, update, options);
};

const countDocuments = async (filter?: Record<string, any>) => {
    return await JobSimulationModel.countDocuments(filter);
};

const aggregate = async (pipeline: PipelineStage[]) => {
    return await JobSimulationModel.aggregate(pipeline);
};

export default {
    findOne,
    findOneAndUpdate,
    countDocuments,
    aggregate,
}