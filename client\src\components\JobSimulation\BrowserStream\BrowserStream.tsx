import React, { memo, useEffect, useRef, useState } from 'react';
import BrowserStreamBrowserBase from './BrowserStreamBrowserBase';
import BrowserStreamCanvas from './BrowserStreamCanvas';

interface BrowserStreamProps {
  wsRef: React.MutableRefObject<WebSocket | null>;
  wsUrl: string;
  goal: string | null;
  browserUrl?: string;
  onReceiveAgentMessage?: (msg: string) => void;
  onReceiveStatusCode?: (code: string) => void;
}
const maxRetries = 3;

const BrowserStream: React.FC<BrowserStreamProps> = ({ wsRef, wsUrl, goal, browserUrl, onReceiveAgentMessage, onReceiveStatusCode }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const goalState = useRef<{ goal?: string, steps?: any[], done?: boolean } | null>(null);
  const [status, setStatus] = useState<string>('Connecting...');
  const [statusCode, setStatusCode] = useState<string>('');
  const [browserEnv, setBrowserEnv] = useState<'LOCAL' | 'BROWSERBASE' | ''>('');
  const [_connected, setConnected] = useState(false);
  const [showReconnectDialog, setShowReconnectDialog] = useState(false);
  const [initUrl] = useState<string>(browserUrl || "https://google.com");
  const [liveViewLink, setLiveViewLink] = useState<string>("");
  const [currentUrl, setCurrentUrl] = useState<string>("");

  const sendWsData = (obj: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      if (obj instanceof String) {
        wsRef.current.send(obj as string);
      } else {
        try {
          wsRef.current.send(JSON.stringify(obj));
        } catch (error) {
          console.error('Error sending data to ws server : ', error);
        }
      }
    }
  }

  useEffect(() => {
    console.log("wsRef.current ::: ", wsUrl, !!wsRef.current);
    if (wsRef.current) return;
    try {
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        setStatus('Connected to server');
        setConnected(true);
        setShowReconnectDialog(false);
      };

      wsRef.current.onmessage = (event: MessageEvent) => {
        // Handle incoming data
        if (typeof event.data === 'string') {
          try {
            const serverEvent = JSON.parse(event.data);
            if (serverEvent.type === 'status') {
              setStatus(serverEvent.message);
              if (serverEvent.code !== undefined) {
                setStatusCode(serverEvent.code);
                onReceiveStatusCode?.(serverEvent.code);
              }

              return;
            }
            if (serverEvent.type === 'url') {
              setCurrentUrl(serverEvent.url);

              return;
            }
            if (serverEvent.type === 'agent_message') {
              onReceiveAgentMessage?.(serverEvent.message);
              return;
            }
            if (serverEvent.type === 'live_view_link' && serverEvent.url) {
              setLiveViewLink(serverEvent.url);
              return;
            }
            if (serverEvent.type === 'browser_env' && serverEvent.env) {
              setBrowserEnv(serverEvent.env);
              return;
            }

            if (serverEvent.type === 'goal_step') {
              const { action, result, steps, done, error } = serverEvent.data;

              if (action === 'START') {
                goalState.current = {
                  goal: "",
                  steps,
                  done: false,
                };
              }

              if (error) {
                // TODO: Handle error
                return;
              }

              if (action === 'GET_NEXT_STEP') {
                goalState.current = {
                  goal: goalState.current?.goal || "",
                  steps,
                  done: false,
                };

                if (!!result?.reasoning) {
                  onReceiveAgentMessage?.(result.reasoning);
                }

                wsRef.current?.send(JSON.stringify({
                  type: 'goal',
                  data: {
                    action: 'EXECUTE_STEP',
                    goal: goalState.current?.goal || "",
                    previousSteps: goalState.current?.steps || [],
                    step: result,
                  }
                }));

                return;
              }
              if (action === 'EXECUTE_STEP') {
                // TODO: Check DONE --> Unlock chat
                // Else call GET_NEXT_STEP
                if (done) {
                  console.log("ALL DONE");
                  // alert("ALL DONE");
                  goalState.current = {
                    ...goalState.current,
                    done: true,
                  }
                } else {
                  wsRef.current?.send(JSON.stringify({
                    type: 'goal',
                    data: {
                      action: 'GET_NEXT_STEP',
                      goal: goalState.current?.goal || "",
                      previousSteps: goalState.current?.steps || [],
                    }
                  }));
                }
                return;
              }
            }
          } catch {
            // Plain text status message
            setStatus(event.data);
          }
          return;
        }

        // Handle binary image data
        if (event.data instanceof Blob) {
          const img = new Image();
          img.onload = () => {
            const canvas = canvasRef.current;
            // const overlayCanvas = overlayCanvasRef.current;

            if (canvas) {
              const ctx = canvas.getContext('2d');
              if (ctx) {
                // Update canvas size to match image
                canvas.width = img.width;
                canvas.height = img.height;
                // overlayCanvas.width = img.width;
                // overlayCanvas.height = img.height;

                // Draw the image
                ctx.drawImage(img, 0, 0);
              }
            }

            // Clean up blob URL
            URL.revokeObjectURL(img.src);
          };
          img.src = URL.createObjectURL(event.data);
        }
      };

      wsRef.current.onerror = () => {
        setStatus('Error connecting to server');
        setConnected(false);
      };

    } catch (error) {
      console.error('WebSocket connection error:', error);
      setStatus('Error creating WebSocket connection');
      setConnected(false);
      setShowReconnectDialog(true);
    }

    // Cleanup
    return () => {
      console.log("Cleanup WebSocket connection");
      wsRef.current?.close();
      wsRef.current = null;
    };
  }, [wsUrl]);

  useEffect(() => {
    if (!goal ||
      wsRef.current?.readyState !== WebSocket.OPEN ||
      goalState.current?.done ||
      goal === goalState.current?.goal ||
      statusCode !== 'running'
    ) {
      return;
    }

    goalState.current = {
      goal,
      steps: [...(goalState.current?.steps || [])],
      done: false,
    };

    sendWsData({
      type: 'goal',
      data: {
        action: 'GET_NEXT_STEP',
        goal: goal,
        previousSteps: goalState.current?.steps || [],
      }
    });
  }, [goal, statusCode, wsRef.current]);

  useEffect(() => {
    if (initUrl) {
      console.log("useEffect send initUrl ::: ", initUrl);
      sendWsData({
        type: 'start_browser',
        data: {
          url: initUrl,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        }
      });
    }
  }, [initUrl, wsRef.current?.readyState]);

  return (
    <div className="browser-stream min-h-screen w-full h-full flex flex-col">
      {/* Reconnect Dialog */}
      {showReconnectDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md">
            <h3 className="text-lg font-bold mb-4 text-red-600">Connection Failed</h3>
            <p className="text-gray-700 mb-4">
              Failed to connect to the WebSocket server after {maxRetries} attempts.
              Please check if the server is running and try again.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
              >
                Reload Page
              </button>
            </div>
          </div>
        </div>
      )}
      {
        statusCode === 'initializing' && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <span className="text-gray-500">Initializing...</span>
          </div>
        )
      }

      {
        statusCode === 'running' && (
          <>
            {
              browserEnv === 'BROWSERBASE' && liveViewLink && (
                <BrowserStreamBrowserBase liveViewLink={liveViewLink} />
              )
            }
            {
              browserEnv === 'LOCAL' && (
                <BrowserStreamCanvas status={status} currentUrl={currentUrl} canvasRef={canvasRef} sendWsData={sendWsData} />
              )
            }
          </>
        )
      }
    </div>
  );
};

export default memo(BrowserStream);
