import { generateSimulationInfo, InputSimulationInfoGeneration } from './simulationInfoGeneration';



describe('generateSimulationInfo', () => {
    beforeEach(() => {
        process.env.OPENAI_API_KEY = '********************************************************************************************************************************************************************';
        process.env.DEEPSEEK_API_KEY = '***********************************';
    });
    it('should generate simulation info', async () => {
        const jobData: InputSimulationInfoGeneration = {
            title: 'REMOTE - Aurion - Configuration & Implementation Consultants & Developers',
            description: '<p></p><p>Our national clients are looking for people with all- round Aurion skills to join their team on a permanent basis to work remotely with everything related to Aurion.</p><p><br></p><p><strong>IT IS ESSENTIAL YOU ARE AN AUSTRALIAN CITIZEN IF APPLYING FOR THIS ROLE.</strong></p><p><br></p><p>Skills Required</p><p>A vital addition to the team, the role will include designing, configuring and implementing Aurion solutions in collaboration with a team of functional and technical consultants. In addition, the successful candidate will possess the following:</p><p><br></p><ul><li>Solid Experience with Aurion Software and hands-on Aurion experience.</li><li><strong>Configuration &amp; Implementation experience is essential</strong></li><li>Excellent understanding of business processes in a SME context</li><li>Experience working within a variety of industries such as distribution, manufacturing or retail</li><li>Must have strong accounting/finance knowledge</li><li>Proven experience providing consulting, implementing, configuring Financial / Payroll software solutions</li><li>Experience running workshops and documenting requirements including design specifications and process mapping</li><li>Superior written and verbal communication skills</li><li>Creativity and problem-solving ability with a can-do attitude</li><li>A Grenadier attitude is essential.</li></ul><p></p>',
            companyName: 'East Coast Resourcing Ltd (ECR)',
            skills: [
                "Problem-solving",
                "Distribution"
            ],
            categories: [
                "it-consulting",
                "software-development",
                "ai-machine-learning",
                "data-science"
            ]
        };
        const result = await generateSimulationInfo(jobData);
        console.log("result ::: ", result);
        expect(result).toBeDefined();
    }, 30000);
});