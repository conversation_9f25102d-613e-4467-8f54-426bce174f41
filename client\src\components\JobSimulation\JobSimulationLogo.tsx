import { useEffect, useRef, useState } from 'react';

interface JobSimulationLogoProps {
  logo: string;
  backgroundSize?: string;
}

export default function JobSimulationLogo({ logo, backgroundSize = '' }: JobSimulationLogoProps) {
  const divRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (divRef.current && logo && ['contain', 'cover'].includes(backgroundSize)) {
      const img = new Image();
      img.src = logo;
      img.onload = () => {
        const isSmall = img.width < 200 || img.height < 200;
        if (divRef.current) {
          divRef.current.style.backgroundSize = isSmall ? 'auto' : backgroundSize;
          divRef.current.style.backgroundImage = `url(${logo})`;
          divRef.current.style.backgroundPosition = 'center';
        }
      };
    }
  }, [divRef, logo, backgroundSize]);

  return (
    <div className="flex flex-row justify-center">
      <img src="/assets/job-simulation/bg-logo-left.png" alt="" className="max-w-[250px]" />
      <div className="relative min-h-[240px] min-w-[240px] overflow-hidden">
        <div
          ref={divRef}
          className="h-full w-full bg-white/50 bg-center bg-no-repeat"
          style={{
            clipPath: 'url(#hexagon-path)',
            // ...(logo ? { backgroundImage: `url(${logo})` } : {}),
            // ...(backgroundSize ? { backgroundSize } : {}),
          }}
        />
        <svg width="0" height="0" className="absolute">
          <clipPath id="hexagon-path" clipPathUnits="objectBoundingBox">
            <path
              d="M0.03,0.5
                        C0.03,0.48 0.032,0.46 0.04,0.443
                        L0.23,0.107
                        C0.238,0.09 0.252,0.075 0.27,0.067
                        L0.73,0.067
                        C0.748,0.075 0.762,0.09 0.77,0.107
                        L0.96,0.443
                        C0.968,0.46 0.97,0.48 0.97,0.5
                        L0.97,0.5
                        C0.97,0.52 0.968,0.54 0.96,0.557
                        L0.77,0.893
                        C0.762,0.91 0.748,0.925 0.73,0.933
                        L0.27,0.933
                        C0.252,0.925 0.238,0.91 0.23,0.893
                        L0.04,0.557
                        C0.032,0.54 0.03,0.52 0.03,0.5Z"
            />
          </clipPath>
        </svg>
      </div>
      <img src="/assets/job-simulation/bg-logo-right.png" alt="" className="max-w-[250px]" />
    </div>
  );
}
