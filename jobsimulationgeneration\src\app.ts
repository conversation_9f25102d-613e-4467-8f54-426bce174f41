import cors from 'cors';
import express, { Application, Request, Response } from 'express';
import { connectDatabase } from './config/database';
import { createBatchWorker, createPriorityWorker } from './queue/jobQueue';
import { createWorker as createNoteGenerationWorker } from './queue/jobSimulationNoteQueue';
import jobRoutes from './routes/jobs';
import statsRoutes from './routes/stats';
import logger from './services/logger';

const app: Application = express();

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' })); // Increased limit for job arrays
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
app.use((req: Request, _res: Response, next) => {
  logger.simple(req.method, req.path);
  next();
});

// Initialize database and worker

let workers: any[] = [];

const initializeServices = async () => {
  try {
    // Connect to MongoDB
    await connectDatabase();

    workers = [
      {
        name: 'Batch Worker',
        worker: createBatchWorker()
      },
      {
        name: 'Priority Worker',
        worker: createPriorityWorker()
      },
      {
        name: 'Note Generation Worker',
        worker: createNoteGenerationWorker()
      }
    ];

    logger.simple('✅ Services initialized successfully', 'MongoDB', 'Redis', workers.map(worker => worker.name).join(', '));

  } catch (error) {
    logger.simple('❌ Failed to initialize services:', error);
    process.exit(1);
  }
};

// Initialize services
initializeServices();

// API Routes
app.use('/api/jobs', jobRoutes);
app.use('/api/stats', statsRoutes);

// Health check endpoint
app.get('/health', (_req: Request, res: Response) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    service: process.env.SERVICE_NAME || 'Job Simulation Generation Engine'
  });
});

// Root endpoint
app.get('/', (_req: Request, res: Response) => {
  res.status(200).json({
    message: 'Welcome to Job Simulation Generation Engine',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      jobs: '/api/jobs',
      stats: '/api/stats',
      // healthCheck: '/api/stats/health'
    },
    // documentation: {
    //   submitJobs: 'POST /api/jobs - Submit jobs for simulation (max 100 per request)',
    //   getJobs: 'GET /api/jobs - List jobs with pagination and filtering',
    //   getJob: 'GET /api/jobs/:id - Get specific job details',
    //   getStats: 'GET /api/stats - Get system statistics',
    //   getHealth: 'GET /api/stats/health - Detailed health check'
    // }
  });
});

// 404 handler
app.use((req: Request, res: Response) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Error handler
app.use((err: Error, _req: Request, res: Response, _next: any) => {
  logger.simple(err.stack);
  res.status(500).json({
    error: 'Internal Server Error',
    message: 'Something went wrong!'
  });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.simple('🛑 SIGTERM received, shutting down gracefully...');
  workers.forEach(async (item: any) => {
    await item.worker?.close?.();
  });
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.simple('🛑 SIGINT received, shutting down gracefully...');
  workers.forEach(async (item: any) => {
    await item.worker?.close?.();
  });
  process.exit(0);
});

export default app;
