const express = require('express');
const router = express.Router();
const JobSimulationController = require('~/server/controllers/JobSimulationController');
const JobSimulationTaskController = require('~/server/controllers/JobSimulationTaskController');
const { requireJwtAuth } = require('~/server/middleware');

router.use(requireJwtAuth);

router.get('/list', JobSimulationController.getUserJobSimulationsInfo);
router.get('/:jobSimulationId', JobSimulationController.getUserJobSimulationInfo);
router.get('/:jobSimulationId/email-task', JobSimulationTaskController.getAvailableTask);
router.post('/:jobSimulationId/submit-task', JobSimulationTaskController.submitTask);

module.exports = router;
