import mongoose, { Document, Schema } from 'mongoose';

export interface IJobSimulationStaff {
  name: string;
  avatar: string;
  role: string;
  roleId: string;
  email: string;
}
export interface IJobSimulationTask {
  id: string;
  title: string;
  description: string;
  level: number;
  order: number;
  prompt?: string;
  example?: string;

  // for jobsimulationprogresses
  status?: string; // "todo" | "passed" | "failed"
  submissions?: any[];
  startedAt?: Date;
  completedAt?: Date;
}

export interface IJobSimulation extends Document {
  jobSimulationId: string;
  jobId?: string;
  isLocal: boolean;
  staffs: IJobSimulationStaff[];
  tasks: IJobSimulationTask[];
}

const JobSimulationSchema: Schema = new Schema({
  jobSimulationId: {
    type: String,
    required: true,
  },
  isLocal: {
    type: Boolean,
    required: true,
    default: true,
  },
  jobId: {
    type: String,
    required: false,
  },
  staffs: {
    type: Schema.Types.Mixed,
    required: true,
    default: [],
  },
  tasks: {
    type: Schema.Types.Mixed,
    required: true,
    default: [],
  },
}, {
  timestamps: true,
  strict: false,
  autoIndex: false
});

export default mongoose.model<IJobSimulation>('jobSimulation', JobSimulationSchema);
