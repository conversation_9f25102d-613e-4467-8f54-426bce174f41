import { TJobSimulationTask } from 'librechat-data-provider';
import { Document, Schema } from 'mongoose';

export interface IJobSimulationStaff {
  name: string;
  avatar: string;
  role: string;
  roleId: string;
  email: string;
}

export interface IJobSimulation extends Document {
  // TODO: change to jobsimUniqueId
  jobSimulationId: string;
  name: string;
  role?: string;
  description?: string;
  banner?: string;
  logo: string;
  companyName?: string;
  billionIntakeCode?: string;
  billionIntakeId?: string;
  agentId: string;
  agentInstructions: string;
  agentDescription?: string;
  virtualWorld?: string;
  skills?: string[];
  credentials: {
    username: string;
    password: string;
  };
  emailTemplateId?: string;
  /**
   * Public: everyone can access,
   * Private: only users know the link can access. TODO: implement logic to generate invitation code
   * Inactive: no one can access
   */
  status: 'public' | 'private' | 'inactive';
  // Real job id (from crawler)
  jobId?: string;
  isLocal: boolean;
  meetingLink?: string;
  appIds?: string[];
  staffs?: IJobSimulationStaff[];
  tasks?: TJobSimulationTask[];
}

const jobSimulationTaskSchema: Schema<TJobSimulationTask> = new Schema({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  level: {
    type: Number,
    required: true,
    default: 0,
  },
  order: {
    type: Number,
    required: true,
    default: 0,
  },
  prompt: {
    type: String,
    required: false,
  },
  example: {
    type: String,
    required: false,
  },
});

const jobSimulationStaffSchema: Schema<IJobSimulationStaff> = new Schema({
  name: {
    type: String,
    required: true,
  },
  avatar: {
    type: String,
    required: true,
  },
  roleId: {
    type: String,
    required: true,
  },
  role: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
  },
});

const jobSimulationSchema: Schema<IJobSimulation> = new Schema({
  // TODO: change to jobsimUniqueId
  jobSimulationId: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  role: {
    type: String,
    required: false,
  },
  skills: {
    type: [String],
    required: false,
    default: undefined,
  },
  description: {
    type: String,
    required: false,
  },
  banner: {
    type: String,
    required: false,
  },
  logo: {
    type: String,
    required: true,
  },
  companyName: {
    type: String,
    required: false,
  },
  billionIntakeCode: {
    type: String,
    required: false,
  },
  billionIntakeId: {
    type: String,
    required: false,
  },
  agentId: {
    type: String,
    required: true,
  },
  agentInstructions: {
    type: String,
    required: true,
  },
  agentDescription: {
    type: String,
    required: true,
  },
  virtualWorld: {
    type: String,
    required: false,
  },
  meetingLink: {
    type: String,
    required: false,
  },
  credentials: {
    username: {
      type: String,
      required: true,
    },
    password: {
      type: String,
      required: true,
    },
  },
  status: {
    // Using enum
    type: String,
    enum: ['public', 'private', 'inactive'],
    required: true,
    default: 'private',
  },
  appIds: {
    type: [String],
    required: false,
  },
  emailTemplateId: {
    type: String,
    required: false,
  },
  tasks: {
    type: [jobSimulationTaskSchema],
    required: false,
  },
  staffs: {
    type: [jobSimulationStaffSchema],
    required: false,
  },
  isLocal: {
    type: Boolean,
    required: true,
  },
  jobId: {
    type: String,
    required: true,
  },
});

jobSimulationSchema.index({ jobSimulationId: 1 }, { unique: true });

export default jobSimulationSchema;
