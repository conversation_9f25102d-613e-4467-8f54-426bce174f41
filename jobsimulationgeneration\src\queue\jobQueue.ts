import { Job as BullJob, Queue, Worker } from 'bullmq';
import { connectionOptions, createRedisConnection } from '../config/redis';
import { IJobSimulationGeneration } from '../models/JobSimulationGeneration';
import jobSimulationGeneration from '../services/jobSimulationGenerationService';
import logger from '../services/logger';
import { processJobSimulation } from '../services/simulationServices/simulationService';
import { sendWebhook } from '../services/webhookService';

const JOB_BATCH_QUEUE_NAME_DEFAULT = 'job-simulation-generation-queue';
const JOB_PRIORITY_QUEUE_NAME_DEFAULT = 'job-simulation-generation-priority-queue';

export interface JobQueueData {
  jobId: string;
}

// Create Redis connection
createRedisConnection();

// Create the queue
export const jobBatchQueue = new Queue<JobQueueData>(
  process.env.JOB_BATCH_QUEUE_NAME || JOB_BATCH_QUEUE_NAME_DEFAULT,
  {
    connection: connectionOptions
  }
);
export const jobPriorityQueue = new Queue<JobQueueData>(
  process.env.JOB_PRIORITY_QUEUE_NAME || JOB_PRIORITY_QUEUE_NAME_DEFAULT,
  {
    connection: connectionOptions
  }
);

// Create the worker with concurrency limit
export const createBatchWorker = (): Worker<JobQueueData> => {
  const maxConcurrentJobs = parseInt(process.env.MAX_CONCURRENT_BATCH_JOBS || '3');

  const worker = new Worker<JobQueueData>(
    process.env.JOB_BATCH_QUEUE_NAME || JOB_BATCH_QUEUE_NAME_DEFAULT,
    async (job: BullJob<JobQueueData>) => {
      const { jobId } = job.data;

      try {
        logger.simple(`Job Processing ::: `, jobId, 'Starting');

        const jobDoc = await jobSimulationGeneration.findOneAndUpdate(
          { jobId: jobId },
          {
            simulationStatus: 'processing',
            updatedAt: new Date()
          }
        );

        // Find the job in database
        if (!jobDoc) {
          throw new Error(`Job not found: ${jobId}`);
        }

        // Process the simulation

        let updatedJob: IJobSimulationGeneration | null = null;
        const simulationResult = await processJobSimulation(jobDoc);

        if (simulationResult.success) {
          updatedJob = simulationResult.data!;
        }

        if (!simulationResult.success) {
          const updateData: any = {
            simulationStatus: 'failed',
            errorMessage: simulationResult.error,
            processedAt: new Date(),
            updatedAt: new Date(),
          };

          updatedJob = await jobSimulationGeneration.findOneAndUpdate({ jobId: jobId }, updateData, { new: true });
        }

        if (!updatedJob) {
          throw new Error(`Updated job not found: ${jobId}`);
        }

        // Send webhook notification
        logger.simple(`Job Processing ::: `, jobId, 'Sending webhook');
        const webhookResult = await sendWebhook(updatedJob);

        // Update webhook status
        await jobSimulationGeneration.findOneAndUpdate(
          { jobId: jobId },
          {
            webhookStatus: webhookResult.success ? 'success' : 'failed',
            webhookRetryCount: webhookResult.retryCount,
            webhookSentAt: new Date(),
            updatedAt: new Date()
          }
        );

        logger.simple(`Job Processing ::: `, jobId, '✅ completed');
        return {
          jobId,
          simulationSuccess: simulationResult.success,
          webhookSuccess: webhookResult.success
        };

      } catch (error) {
        logger.simple(`Job Processing ::: `, jobId, '❌ Failed', error);

        // Update job status to failed
        try {
          await jobSimulationGeneration.findOneAndUpdate(
            { jobId: jobId },
            {
              simulationStatus: 'failed',
              errorMessage: error instanceof Error ? error.message : 'Unknown error',
              processedAt: new Date(),
              updatedAt: new Date()
            }
          );
        } catch (error) { logger.simple(`Job Processing ::: `, jobId, '❌ Failed to update job status', error); }

        throw error;
      }
    },
    {
      connection: connectionOptions,
      concurrency: maxConcurrentJobs,
      removeOnComplete: { count: 50 }, // Keep last 50 completed jobs
      removeOnFail: { count: 50 }       // Keep last 50 failed jobs
    }
  );

  // Worker event handlers
  worker.on('completed', (job) => {
    logger.simple(`✅ Batch Worker completed job ${job.id} with data:`, job.returnvalue);
  });

  worker.on('failed', (job, err) => {
    logger.simple(`❌ Batch Worker failed job ${job?.id}:`, err);
  });

  worker.on('error', (err) => {
    logger.simple('❌ Batch Worker error:', err);
  });

  return worker;
};

export const createPriorityWorker = (): Worker<JobQueueData> => {
  const maxConcurrentJobs = parseInt(process.env.MAX_CONCURRENT_PRIORITY_JOBS || '3');

  const worker = new Worker<JobQueueData>(
    process.env.JOB_PRIORITY_QUEUE_NAME || JOB_PRIORITY_QUEUE_NAME_DEFAULT,
    async (job: BullJob<JobQueueData>) => {
      const { jobId } = job.data;

      try {
        logger.simple(`Priority Job Processing ::: `, jobId, 'Starting');

        const jobDoc = await jobSimulationGeneration.findOneAndUpdate(
          { jobId: jobId },
          {
            simulationStatus: 'processing',
            updatedAt: new Date()
          }
        );

        // Find the job in database
        if (!jobDoc) {
          throw new Error(`Job not found: ${jobId}`);
        }

        // Process the simulation

        let updatedJob: IJobSimulationGeneration | null = null;
        const simulationResult = await processJobSimulation(jobDoc);

        if (simulationResult.success) {
          updatedJob = simulationResult.data!;
        }

        if (!simulationResult.success) {
          const updateData: any = {
            simulationStatus: 'failed',
            errorMessage: simulationResult.error,
            processedAt: new Date(),
            updatedAt: new Date(),
          };

          updatedJob = await jobSimulationGeneration.findOneAndUpdate({ jobId: jobId }, updateData, { new: true });
        }

        if (!updatedJob) {
          throw new Error(`Updated job not found: ${jobId}`);
        }

        // Send webhook notification
        logger.simple(`Priority Job Processing ::: `, jobId, 'Sending webhook');
        const webhookResult = await sendWebhook(updatedJob);

        // Update webhook status
        await jobSimulationGeneration.findOneAndUpdate(
          { jobId: jobId },
          {
            webhookStatus: webhookResult.success ? 'success' : 'failed',
            webhookRetryCount: webhookResult.retryCount,
            webhookSentAt: new Date(),
            updatedAt: new Date()
          }
        );

        logger.simple(`Priority Job Processing ::: `, jobId, '✅ completed');
        return {
          jobId,
          simulationSuccess: simulationResult.success,
          webhookSuccess: webhookResult.success
        };

      } catch (error) {
        logger.simple(`Priority Job Processing ::: `, jobId, '❌ Failed', error);

        // Update job status to failed
        try {
          await jobSimulationGeneration.findOneAndUpdate(
            { jobId: jobId },
            {
              simulationStatus: 'failed',
              errorMessage: error instanceof Error ? error.message : 'Unknown error',
              processedAt: new Date(),
              updatedAt: new Date()
            }
          );
        } catch (error) { logger.simple(`Priority Job Processing ::: `, jobId, '❌ Failed to update job status', error); }

        throw error;
      }
    },
    {
      connection: connectionOptions,
      concurrency: maxConcurrentJobs,
      removeOnComplete: { count: 50 }, // Keep last 50 completed jobs
      removeOnFail: { count: 50 }       // Keep last 50 failed jobs
    }
  );

  // Worker event handlers
  worker.on('completed', (job) => {
    logger.simple(`✅ Priority Worker completed job ${job.id} with data:`, job.returnvalue);
  });

  worker.on('failed', (job, err) => {
    logger.simple(`❌ Priority Worker failed job ${job?.id}:`, err);
  });

  worker.on('error', (err) => {
    logger.simple('❌ Priority Worker error:', err);
  });

  return worker;
};

// Add job to queue
export const addJobToBatchQueue = async (jobId: string): Promise<void> => {
  try {
    const checkJobInQueue = await jobPriorityQueue.getJob(jobId);
    if (checkJobInQueue?.isActive() || checkJobInQueue?.isWaiting()) {
      return;
    } else if (checkJobInQueue) {
      await checkJobInQueue.remove();
    }

    await jobBatchQueue.add(
      'process-simulation',
      { jobId },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: true,
        removeOnFail: false
      }
    );

    logger.simple(`Job added to queue: ${jobId}`);
  } catch (error) {
    logger.simple(`Failed to add job to queue: ${jobId}`, error);
    throw error;
  }
};

export const addJobToPriorityQueue = async (jobId: string): Promise<void> => {
  try {
    const checkJobInQueue = await jobBatchQueue.getJob(jobId);
    if (checkJobInQueue?.isActive()) {
      return;
    } else {
      await checkJobInQueue?.remove();
    }
    await jobPriorityQueue.add(
      'process-simulation',
      { jobId },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: true,
        removeOnFail: false
      }
    );

    logger.simple(`Job added to priority queue: ${jobId}`);
  } catch (error) {
    logger.simple(`Failed to add job to priority queue: ${jobId}`, error);
    throw error;
  }
};
