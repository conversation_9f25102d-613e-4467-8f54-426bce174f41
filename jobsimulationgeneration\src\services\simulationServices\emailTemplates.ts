const templates = [
    {
        templateId: "688dac1aefdbd9ef3a4b77b1",
        emails: [
            {
                id: "1",
                type: "welcome",
                name: "{{hrName}}",
                avatar: "{{hrAvatar}}",
                role: "{{hrRole}}",
                email: "{{hrEmail}}",
                title: "Welcome to {{companyName}}'s {{simulationName}}!",
                desc: "We're thrilled to have you onboard for this immersive experience.",
                nextEmailId: "2",
                data: {
                    logo: "{{logo}}",
                    greeting: "Hi {{userName}}",
                    content: `Welcome to **{{companyName}}!** We're excited to have you join our **{{simulationName}}**.
                    
This simulation is designed to provide you with hands-on experience in digital marketing strategies, content creation, and data analysis. You'll be working on real-world tasks that reflect the dynamic nature of our industry.

To get started, please check your inbox for a meeting invitation from your {{managerR<PERSON>}}, **{{managerName}}**.

We look forward to seeing your creativity and analytical skills in action!`,
                    signature: {
                        title: "Regards",
                        company: "{{companyName}}"
                    }
                },
                triggerActions: [
                    {
                        type: "nextEmail",
                        data: {
                            nextEmailId: "2",
                            "triggerTimeout": 3,
                            when: "open"
                        }
                    }
                ]
            },
            {
                id: "2",
                type: "meeting",
                name: "{{managerName}}",
                avatar: "{{managerAvatar}}",
                role: "{{managerRole}}",
                email: "{{managerEmail}}",
                title: "Kick-off Meeting: {{simulationName}}",
                desc: "Let's connect to discuss your role and upcoming tasks.",
                nextEmailId: "3",
                data: {
                    logo: "{{logo}}",
                    greeting: "Hi {{userName}}",
                    content: `I'm **{{managerName}}**, your {{managerRole}} at {{companyName}}.
We're excited to kick off your {{simulationName}} with an introductory meeting.
In this brief session, you'll learn about **your role and upcoming tasks**, and get set up for success.

I look forward to meeting you live and officially kicking things off together!`,
                    actions: [
                        {
                            type: "joinMeeting",
                            label: "Join the Meeting",
                            title: "Onboarding Meeting",
                            data: {
                                datetime: "{{emailTime}}",
                                duration: "~1 minute",
                                meetingLink: "{{meetingLink}}",
                                completionMeetingActions: [
                                    {
                                        type: "triggerAssistant",
                                        data: {
                                            triggerMessage: "I have completed the meeting with {{managerName}}."
                                        }
                                    },
                                    {
                                        type: "enableApps",
                                        data: {
                                            appIds: ["mail", "meeting", "task-board"]
                                        }
                                    },
                                    {
                                        type: "sendEmailTask"
                                    }
                                ]
                            }
                        }
                    ],
                    signature: {
                        title: "Warm regards",
                        company: "{{companyName}}"
                    }
                },
                triggerActions: [
                    {
                        type: "triggerAssistant",
                        data: {
                            "triggerTimeout": 1,
                            triggerMessage: "I've received the meeting invitation.",
                            when: "receive"
                        }
                    },
                    {
                        type: "enableApps",
                        data: {
                            appIds: ["mail", "meeting", "task-board"],
                            when: "receive"
                        }
                    }
                ]
            },
            {
                id: "3",
                type: "congratulation",
                name: "{{managerName}}",
                avatar: "{{managerAvatar}}",
                role: "{{managerRole}}",
                email: "{{managerEmail}}",
                title: "Thank You for Joining the {{simulationName}}",
                desc: "Congratulations on completing the simulation!",
                data: {
                    logo: "{{logo}}",
                    greeting: "Hi {{userName}}",
                    content: `Congratulations on successfully completing the {{simulationName}} at {{companyName}}!

You've demonstrated valuable skills and a strong commitment throughout the simulation.  
We hope this experience has given you insights and practical knowledge to support your future goals.

Wishing you continued success on your career journey!
    `,
                    signature: {
                        title: "Regards",
                        company: "{{companyName}}"
                    }
                }
            },
            {
                id: `email-task:{{taskId}}`,
                type: 'task',
                name: "{{managerName}}",
                avatar: "{{managerAvatar}}",
                role: "{{managerRole}}",
                email: "{{managerEmail}}",
                title: `Task: {{taskName}}`,
                desc: `New task assigned: {{taskName}}`,
                data: {
                    taskId: "{{taskId}}",
                    logo: "{{logo}}",
                    greeting: "Hi {{userName}}",
                    content: `You're doing great so far in the {{simulationName}} at {{companyName}}!
Your next task is titled: **{{taskName}}**

{{taskDescription}}

Please review the task carefully and complete it at your earliest convenience.
**Simply reply to this email with your completed task.**

Good luck — we look forward to seeing your work!`,
                    actions: [],
                    signature: {
                        title: "Best regards",
                        company: "{{companyName}}"
                    }
                },
                allowReply: true,
                triggerActions: [
                    {
                        type: "triggerAssistant",
                        data: {
                            triggerTimeout: 1,
                            triggerMessage: `I've received the task {{taskName}}`,
                            when: "receive"
                        }
                    },
                    {
                        type: "enableApps",
                        data: {
                            appIds: ["mail", "meeting", "task-board"],
                            when: "receive"
                        }
                    }
                ]
            }
        ]
    },
    {
        templateId: "688db103efdbd9ef3a4b77b2",
        emails: [
            {
                id: "1",
                type: "welcome",
                name: "{{hrName}}",
                avatar: "{{hrAvatar}}",
                role: "{{hrRole}}",
                email: "{{hrEmail}}",
                title: "You're Invited to Join {{companyName}}'s {{simulationName}}",
                desc: "We're excited to have you begin this journey with us.",
                nextEmailId: "2",
                data: {
                    logo: "{{logo}}",
                    greeting: "Hello {{userName}},",
                    content: `Welcome aboard!
                    
You've been selected to participate in the **{{simulationName}}** here at **{{companyName}}**. This experience has been carefully designed to expose you to realistic, practical challenges in your role.

Throughout this simulation, you'll engage in tasks that reflect real-world workflows and expectations.

Be sure to check your inbox shortly for a meeting invitation from your manager, **{{managerName}}**, where everything will be introduced.

We look forward to seeing your potential in action!`,
                    signature: {
                        title: "Sincerely",
                        company: "{{companyName}}"
                    }
                },
                triggerActions: [
                    {
                        type: "nextEmail",
                        data: {
                            nextEmailId: "2",
                            "triggerTimeout": 3,
                            when: "open"
                        }
                    }
                ]
            },
            {
                id: "2",
                type: "meeting",
                name: "{{managerName}}",
                avatar: "{{managerAvatar}}",
                role: "{{managerRole}}",
                email: "{{managerEmail}}",
                title: "Let's Meet: {{simulationName}} Kickoff",
                desc: "Your orientation meeting is ready.",
                nextEmailId: "3",
                data: {
                    logo: "{{logo}}",
                    greeting: "Hi {{userName}},",
                    content: `I'm **{{managerName}}**, your manager at **{{companyName}}**.
                    
To get you started, we've scheduled a short orientation call where I'll walk you through the simulation's structure and your responsibilities.

Please join this meeting at the scheduled time to get familiar with your workspace and the tasks ahead.

Looking forward to welcoming you in person!`,
                    actions: [
                        {
                            type: "joinMeeting",
                            label: "Join Orientation",
                            title: "Orientation Call",
                            data: {
                                datetime: "{{emailTime}}",
                                duration: "~1 minute",
                                meetingLink: "{{meetingLink}}",
                                completionMeetingActions: [
                                    {
                                        type: "triggerAssistant",
                                        data: {
                                            triggerMessage: "I've completed the kickoff meeting with {{managerName}}."
                                        }
                                    },
                                    {
                                        type: "enableApps",
                                        data: {
                                            "appIds": ["mail", "meeting", "task-board"]
                                        }
                                    },
                                    {
                                        type: "sendEmailTask"
                                    }
                                ]
                            }
                        }
                    ],
                    signature: {
                        title: "Warm regards",
                        company: "{{companyName}}"
                    }
                },
                triggerActions: [
                    {
                        type: "triggerAssistant",
                        data: {
                            "triggerTimeout": 1,
                            triggerMessage: "I received the kickoff meeting invite.",
                            when: "receive"
                        }
                    },
                    {
                        type: "enableApps",
                        data: {
                            "appIds": ["mail", "meeting", "task-board"],
                            when: "receive"
                        }
                    }
                ]
            },
            {
                id: "3",
                type: "congratulation",
                name: "{{managerName}}",
                avatar: "{{managerAvatar}}",
                role: "{{managerRole}}",
                email: "{{managerEmail}}",
                title: "Well Done - You've Completed the {{simulationName}}!",
                desc: "Thank you for your time and effort!",
                data: {
                    logo: "{{logo}}",
                    greeting: "Hi {{userName}},",
                    content: `You've officially completed the **{{simulationName}}** at **{{companyName}}** - congratulations!
                    
Your participation and dedication throughout this simulation have not gone unnoticed. We hope this experience gave you practical knowledge, confidence, and perspective for your next steps.

Thanks again for being part of the program, and we wish you all the best!`,
                    signature: {
                        title: "Best wishes",
                        company: "{{companyName}}"
                    }
                }
            },
            {
                id: "email-task:{{taskId}}",
                type: "task",
                name: "{{managerName}}",
                avatar: "{{managerAvatar}}",
                role: "{{managerRole}}",
                email: "{{managerEmail}}",
                title: "New Task Assigned: {{taskName}}",
                desc: "You've received a new task in your simulation.",
                data: {
                    taskId: "{{taskId}}",
                    logo: "{{logo}}",
                    greeting: "Hello {{userName}},",
                    content: `It's time for your next step in the **{{simulationName}}**.
                    
The task assigned is: **{{taskName}}**

{{taskDescription}}

When you're ready, simply reply to this email with your completed work. Don't hesitate to ask if anything is unclear.

We're looking forward to reviewing your submission!`,
                    signature: {
                        title: "Kind regards",
                        company: "{{companyName}}"
                    }
                },
                allowReply: true,
                triggerActions: [
                    {
                        type: "triggerAssistant",
                        data: {
                            "triggerTimeout": 1,
                            triggerMessage: "I've received the task {{taskName}}",
                            when: "receive"
                        }
                    },
                    {
                        type: "enableApps",
                        data: {
                            "appIds": ["mail", "meeting", "task-board"],
                            when: "receive"
                        }
                    }
                ]
            }
        ]
    }
]

const staffs = {
    hr: [
        {
            id: '688db770efdbd9ef3a4b77ba',
            name: "HR Team",
            avatar: "https://d14ciuzrn5ydd5.cloudfront.net/bitmeet/tutor-ai/image-3.jpg",
            role: "HR Manager",
            roleId: "hr",
            email: "<EMAIL>"
        },
        {
            id: '688db777efdbd9ef3a4b77bb',
            name: "HR Team",
            avatar: "https://d14ciuzrn5ydd5.cloudfront.net/bitmeet/tutor-ai/image-2.jpg",
            role: "HR Manager",
            roleId: "hr",
            email: "<EMAIL>"
        }
    ],
    manager: [
        {
            id: '688db731efdbd9ef3a4b77b3',
            name: "Emma Kate",
            avatar: "https://beta.bitmeet.io/files/1752675663848-190108921-emmakate_mobile_testing.jpg",
            role: "Team Lead",
            roleId: "manager",
            email: "<EMAIL>"
        },
        {
            id: '688db73aefdbd9ef3a4b77b4',
            name: "Emily Carter",
            avatar: "https://beta.bitmeet.io/files/1752675663848-190108921-emmakate_mobile_testing.jpg",
            role: "Manager",
            roleId: "manager",
            email: "<EMAIL>"
        },
        // {
        //     id: '688db742efdbd9ef3a4b77b5',
        //     name: "Julie Tan",
        //     avatar: "https://beta.bitmeet.io/files/1754117503514-389674775-julie.jpeg",
        //     role: "Team Lead",
        //     email: "<EMAIL>"
        // },
        {
            id: '688db74aefdbd9ef3a4b77b6',
            name: "Anna Nguyen",
            avatar: "https://beta.bitmeet.io/files/1754117503514-389674775-julie.jpeg",
            role: "Manager",
            roleId: "manager",
            email: "<EMAIL>"
        },
        // {
        //     id: '688db753efdbd9ef3a4b77b7',
        //     name: "Alex Chen",
        //     avatar: "https://beta.bitmeet.io/files/1748226552291-98529265-digital_marketing_onboard.jpg",
        //     role: "Manager",
        //     email: "<EMAIL>"
        // },
        // {
        //     id: '688db75defdbd9ef3a4b77b8',
        //     name: "Liam Parker",
        //     avatar: "https://beta.bitmeet.io/files/1748226552291-98529265-digital_marketing_onboard.jpg",
        //     role: "Team Lead",
        //     email: "<EMAIL>"
        // },
        {
            id: '688db766efdbd9ef3a4b77b9',
            name: "Thomas Reed",
            avatar: "https://beta.bitmeet.io/files/1748226552291-98529265-digital_marketing_onboard.jpg",
            role: "Manager",
            roleId: "manager",
            email: "<EMAIL>"
        },
    ]
}

const meetingLinks: { [key: string]: string } = {
    '688db731efdbd9ef3a4b77b3': "https://dev-bitmeet.mnet.io/introduction/2be-9fc-752?t=mi",
    '688db73aefdbd9ef3a4b77b4': "https://dev-bitmeet.mnet.io/introduction/341-2ad-f35?t=mi",
    '688db74aefdbd9ef3a4b77b6': "https://dev-bitmeet.mnet.io/introduction/274-633-b18?t=mi",
    '688db766efdbd9ef3a4b77b9': "https://dev-bitmeet.mnet.io/introduction/ab1-ba6-f64?t=mi",
}

const randomHR = () => {
    const randomIndex = Math.floor(Math.random() * staffs.hr.length);
    return staffs.hr[randomIndex];
}

const randomManager = () => {
    const randomIndex = Math.floor(Math.random() * staffs.manager.length);
    return staffs.manager[randomIndex];
}

const randomEmailTemplate = () => {
    const randomIndex = Math.floor(Math.random() * templates.length);
    return templates[randomIndex];
}

const getMeetingLink = (managerId: string) => {
    return meetingLinks[managerId];
}

const getAgent = () => {
    return {
        id: 'agent_IQVzdq9-ORVgj53H5-J_9',
        name: 'Thomas Lane',
    }
}

export { randomHR, randomManager, randomEmailTemplate, getMeetingLink, getAgent };
