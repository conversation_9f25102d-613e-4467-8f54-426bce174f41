import * as Ariakit from '@ariakit/react';
import { LogOut, Moon, Sun } from 'lucide-react';
import { useContext, useState } from 'react';
import type * as t from '~/common';
import { DropdownMenuSeparator, DropdownPopup } from '~/components';
import { ThemeContext, useAuthContext, useMediaQuery } from '~/hooks';
import useAvatar from '~/hooks/Messages/useAvatar';

const UserProfileDropdown = () => {
  const [isPopoverActive, setIsPopoverActive] = useState(false);
  const { theme, setTheme } = useContext(ThemeContext);
  const { user, logout } = useAuthContext();
  const isSmallScreen = useMediaQuery('(max-width: 768px)');
  const menuId = 'user-profile-menu';

  const avatarSrc = useAvatar(user);
  const avatarSeed = user?.avatar || user?.name || user?.username || '';

  const themeHandler = () => {
    const nextTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(nextTheme);
    setIsPopoverActive(false);
  };

  const logoutHandler = () => {
    logout(`${window.origin}/login`);
  };

  const dropdownItems: t.MenuItemProps[] = [
    {
      // label: user?.email,
      render: (_props) => (
        <div className="p-2">
          {user?.email}
          <DropdownMenuSeparator />
        </div>
      ),
      hideOnClick: false,
    },
    {
      label: theme === 'dark' ? 'Light Theme' : 'Dark Theme',
      onClick: themeHandler,
      icon: {
        dark: <Sun className="icon-md mr-2 text-text-secondary" />,
        light: <Moon className="icon-md mr-2 text-text-secondary" />,
      }[theme],
      hideOnClick: true,
    },
    {
      label: 'Logout',
      onClick: logoutHandler,
      icon: <LogOut className="icon-md mr-2 text-text-secondary" />,
      hideOnClick: true,
    },
  ];

  return (
    <div className="flex items-center">
      <DropdownPopup
        menuId={menuId}
        focusLoop={true}
        isOpen={isPopoverActive}
        setIsOpen={setIsPopoverActive}
        trigger={
          <Ariakit.MenuButton
            id="user-profile-button"
            aria-label="User profile options"
            className="flex items-center gap-2 rounded-lg bg-white p-2 text-text-primary shadow-sm transition-all ease-in-out hover:bg-surface-tertiary dark:bg-gray-700 dark:text-white"
          >
            <div className="relative h-8 w-8 overflow-hidden rounded-full">
              {!avatarSeed ? (
                <div
                  className="relative flex h-full w-full items-center justify-center rounded-full bg-primary/10 p-1 text-text-primary"
                  aria-hidden="true"
                >
                  <svg
                    stroke="currentColor"
                    fill="none"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-5 w-5"
                    height="1em"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                </div>
              ) : (
                <img
                  className="h-full w-full rounded-full object-cover"
                  src={(user?.avatar ?? '') || avatarSrc}
                  alt={`${user?.name || user?.username || user?.email || ''}'s avatar`}
                />
              )}
            </div>
            <span className="hidden text-sm md:block">
              {user?.name ?? user?.username ?? 'User'}
            </span>
          </Ariakit.MenuButton>
        }
        items={dropdownItems}
        className={isSmallScreen ? '' : 'absolute bottom-0 left-0'}
      />
    </div>
  );
};

export default UserProfileDropdown;
