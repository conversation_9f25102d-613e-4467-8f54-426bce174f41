import { Types } from 'mongoose';
import { IJobSimulationGeneration } from '../../models/JobSimulationGeneration';
import jobSimulationGeneration from '../jobSimulationGenerationService';
import jobSimulation from '../jobSimulationService';
import logger from '../logger';
import { generateAgentInfo } from './agentInfoGeneration';
import { getAgent, getMeetingLink, randomEmailTemplate, randomHR, randomManager } from './emailTemplates';
import { generateSimulationInfo, OutputSimulationInfoGeneration } from './simulationInfoGeneration';
import { generateTasks } from './tasksGeneration';

export interface SimulationResult {
  success: boolean;
  data?: IJobSimulationGeneration;
  error?: string;
}

const getGeneralData = () => {
  const hr = randomHR();
  const manager = randomManager();
  const emailTemplate = randomEmailTemplate();
  const meetingLink = getMeetingLink(manager.id);
  const agent = getAgent();

  return {
    hr,
    manager,
    emailTemplate,
    meetingLink,
    agent,
  }
}

const generateJobSimulationInfo = async (job: IJobSimulationGeneration) => {
  return await generateSimulationInfo(job);
}

const generateJobSimulationAgentInfo = async (generalData: any, job: IJobSimulationGeneration, simulation: OutputSimulationInfoGeneration) => {
  return await generateAgentInfo({
    title: simulation.name,
    description: job.description,
    companyName: job.companyName,
    agentName: generalData.agent.name,
    hrName: generalData.hr.name,
    managerName: generalData.manager.name,
    managerRole: generalData.manager.role,
    skills: job.skills,
    categories: job.categories,
  });
}

const generateJobSimulationTasks = async (job: IJobSimulationGeneration, simulation: OutputSimulationInfoGeneration) => {
  return await generateTasks({
    title: simulation.name,
    description: job.description,
    level: simulation.level,
    minutes: simulation.minutes,
    skills: job.skills,
    categories: job.categories,
  });
}

const createDataForSimulation = async (job: IJobSimulationGeneration) => {
  const generalData = getGeneralData();
  const generatedSimulation = await generateJobSimulationInfo(job);

  const [generatedAgentInfo, generatedTasks, simulationByJobId] = await Promise.all([
    generateJobSimulationAgentInfo(generalData, job, generatedSimulation),
    generateJobSimulationTasks(job, generatedSimulation),
    jobSimulation.findOne({ jobId: job.jobId }),
  ]);

  const processedAt = new Date();

  const updateJobSimulationGeneration = jobSimulationGeneration.findOneAndUpdate(
    { jobId: job.jobId },
    {
      simulationData: {
        jobId: job.jobId,
        jobSimulationId: simulationByJobId?.jobSimulationId || generatedSimulation.jobSimulationId,
        level: generatedSimulation.level,
        minutes: generatedSimulation.minutes,
        minute: generatedSimulation.minutes,
        emailTemplateId: generalData.emailTemplate.templateId,
      },
      simulationStatus: 'completed',
      processedAt,
      updatedAt: processedAt,
    },
    {
      new: true,
    }
  );

  // TODO: Implement logic force update. If want to force update, need to pass current jobSimulationId & forceUpdate: true
  const updateJobSimulation = jobSimulation.findOneAndUpdate(
    { jobSimulationId: simulationByJobId?.jobSimulationId || generatedSimulation.jobSimulationId },
    {
      jobId: job.jobId,
      isLocal: true,
      name: generatedSimulation.name,
      description: generatedSimulation.description,
      level: generatedSimulation.level,
      minutes: generatedSimulation.minutes,
      ...(!!job.skills?.length ? { skills: [...new Set(job.skills)] } : {}),
      status: 'public',

      credentials: {
        username: generatedSimulation.username,
        password: generatedSimulation.password,
      },
      companyName: job.companyName,
      logo: job.companyLogo,

      emailTemplateId: generalData.emailTemplate.templateId,
      meetingLink: generalData.meetingLink,

      staffs: [generalData.hr, generalData.manager],
      tasks: (generatedTasks.tasks || []).map((task, index) => {
        (task as any).order = index + 1;
        (task as any).id = new Types.ObjectId().toString();
        return task;
      }),

      agentId: generalData.agent.id,
      agentInstructions: generatedAgentInfo.agentInstructions,
      agentDescription: generatedAgentInfo.agentDescription,
    },
    { new: true, upsert: true }
  );

  // TODO: Transaction (???)

  const [newJobSimulationGeneration] = await Promise.all([
    updateJobSimulationGeneration,
    updateJobSimulation,
  ]);

  return newJobSimulationGeneration;
}

export const processJobSimulation = async (job: IJobSimulationGeneration): Promise<SimulationResult> => {
  try {
    logger.simple(`Processing simulation for job: ${job.jobId} - ${job.title}`);

    let result = null;
    let errorMessage = "";
    try {
      result = await createDataForSimulation(job);
    } catch (error) {
      errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    }


    if (!!result) {
      logger.simple(`✅ Simulation completed successfully for job: ${job.jobId}`);
      return {
        success: true,
        data: result
      };
    } else {
      errorMessage = errorMessage || 'Simulation processing failed due to insufficient data';
      logger.simple(`❌ Simulation failed for job: ${job.jobId} - ${errorMessage}`);
      return {
        success: false,
        error: errorMessage
      };
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    logger.simple(`❌ Error processing simulation for job ${job.jobId}:`, error);
    return {
      success: false,
      error: errorMessage
    };
  }
};
