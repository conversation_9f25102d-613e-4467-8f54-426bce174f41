import React, { useEffect, useRef, useState } from 'react';
import SunEditor from 'suneditor-react';
import 'suneditor/dist/css/suneditor.min.css';
import { SunEditorOptions } from 'suneditor/src/options';

interface ITextEditorProps {
  onChange: (value: string) => void;
  placeholder?: string;
}

const editorOptions = {
  height: 200,
  resizeEnable: false,
  resizingBar: false,
  buttonList: [
    ['removeFormat'],
    ['bold', 'underline', 'italic'],
    ['list'],
    ['table'],
    ['fullScreen'],
  ],
};

export const TextEditor = (props: ITextEditorProps) => {
  const { onChange, placeholder } = props;
  //   const editorRef = useRef<HTMLDivElement>();
  const contentRef = useRef<HTMLDivElement>(null);
  const [value, setValue] = useState('');
  useEffect(() => {
    // console.log(editorRef.current.editor);
  }, []);

  // const onImageUploadError = (errorMessage, result, core) => {
  //   alert(errorMessage);
  // core.noticeOpen(errorMessage);
  // return false;
  // console.log('error!')
  // return true;
  // }

  //   useEffect(() => {
  //     if (!contentRef.current) return;
  //     contentRef.current.innerHTML = value;
  //   }, [value]);

  const onChangeHandler = (content) => {
    // console.log('onChangeHandler ::: ', content);
    onChange(content);
    // setValue(content);
  };
  // const onClickHandler = () => {
  //   if(!contentRef.current) return;
  //   contentRef.current.innerHTML = valueRef.current;
  // }
  return (
    <div>
      <SunEditor
        setOptions={editorOptions as unknown as SunEditorOptions}
        onChange={onChangeHandler}
        placeholder={placeholder || ''}
        setDefaultStyle="font-family: Inter, sans-serif; font-size: 16px"
      />
      {/* <button onClick={onClickHandler} type="button">parsear</button> */}
      <div ref={contentRef}></div>
    </div>
  );
};
