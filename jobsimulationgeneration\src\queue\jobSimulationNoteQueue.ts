import { Job as BullJ<PERSON>, Queue, Worker } from 'bullmq';
import { connectionOptions, createRedisConnection } from '../config/redis';
import logger from '../services/logger';
import jobSimulationService from '../services/jobSimulationService';
import jobSimulationProgressService from '../services/jobSimulationProgressService';
import jobSimulationGenerationService from '../services/jobSimulationGenerationService';
import { IJobSimulationGeneration } from '../models/JobSimulationGeneration';
import { IJobSimulation } from '../models/JobSimulation';
import { IJobSimulationProgress } from '../models/JobSimulationProgress';
import { z } from "zod";
import { generateObject } from "ai";
import { openai } from "@ai-sdk/openai"; // hoặc dùng deepseek nếu muốn
import { deepseek } from "@ai-sdk/deepseek";
import { match } from 'assert';

createRedisConnection();
const QUEUE_NAME_DEFAULT = 'job-simulation-note-generation-queue';

export interface JobSimulationNoteData {
  jobId: string;
  progressId: string;
}

// Create Redis connection

export const jobSimulationNoteQueue = new Queue<JobSimulationNoteData>(
  process.env.JOB_SIMULATION_NOTE_GENERATION_QUEUE_NAME || QUEUE_NAME_DEFAULT,
  {
    connection: connectionOptions
  }
);

const createSystemPrompt = () => {
  return `You are an expert talent evaluator specializing in analyzing performance in job simulations.

Your task is to generate a structured evaluation note based on the given simulation data. The note will be reviewed by recruiters and employers to assess the candidate's job readiness, skills demonstrated, and overall performance.

Guidelines:
1. Carefully analyze each task, including multiple submissions (if any). Focus on the latest submission with "passed" status, or the most recent one if none are passed.
2. Identify strengths, skills demonstrated, and any signs of improvement or resilience.
3. For each skill demonstrated, provide a structured object with:
  - name: the skill name
  - rating: a score from 1 to 5
4. Use a professional tone and objective language.
5. Summarize the candidate's overall performance, skill highlights, and hiring potential.
6. Generate a match percentage score for the candidate's performance.
`;
}

const createUserPrompt = (jsonData: string) => {
  return `Evaluate and generate a structured evaluation note for the following job simulation data:
  ${jsonData}
`
}

// 1. Define output schema
const EvaluationSchema = z.object({
  summary: z.string(),
  skillsDemonstrated: z.object({
    technical: z.array(z.object({ name: z.string(), rating: z.number().min(1).max(5) })),
    softSkills: z.array(z.object({ name: z.string(), rating: z.number().min(1).max(5) }))
  }),
  strengths: z.array(z.string()),
  areasForImprovement: z.array(z.string()).optional(),
  finalAssessment: z.string(),
  matchPercentage: z.number().min(0).max(100)
});

const buildInputData = (jobSimulationGeneration: IJobSimulationGeneration, jobSimulation: IJobSimulation, userProgress: IJobSimulationProgress) => {
  const mapProgressTasks = (userProgress.tasks || []).reduce((acc, curr) => {
    acc[curr.id] = curr;
    return acc;
  }, {} as Record<string, (typeof userProgress.tasks)[0]>);
  return {
    companyName: jobSimulationGeneration.companyName,
    jobTitle: jobSimulationGeneration.title,
    jobDescription: jobSimulationGeneration.description,
    ...(!!jobSimulationGeneration.skills?.length ? { jobSkills: jobSimulationGeneration.skills } : {}),
    ...(!!jobSimulationGeneration.categories?.length ? { jobCategories: jobSimulationGeneration.categories } : {}),

    simulationStartedAt: userProgress.createdAt.toISOString(),
    ...(userProgress.status === 'completed' && userProgress.completedAt ? { simulationCompletedAt: userProgress.completedAt.toISOString() } : {}),
    ...(userProgress.completionMins ? { simulationCompletionMinutes: userProgress.completionMins } : {}),

    tasks: jobSimulation.tasks.map(task => {
      let taskStartedAt = "";
      let taskCompletedAt = "";
      try {
        const progressEmailTask = (userProgress.emails || []).find(emailTask => emailTask.type === 'task' && emailTask.id === `email-task:${task.id}`);
        const passedSubmission = mapProgressTasks[task.id].submissions?.find(sub => sub.status === 'passed');
        taskStartedAt = progressEmailTask?.time ? new Date(progressEmailTask.time).toISOString() : "";
        taskCompletedAt = passedSubmission?.submittedAt ? passedSubmission?.submittedAt?.toISOString() : "";
      } catch (error) { }

      return {
        taskId: task.id,
        taskName: task.title,
        taskDescription: task.description,
        taskStatus: mapProgressTasks[task.id]?.status || 'todo',
        submissions: mapProgressTasks[task.id]?.submissions || [],
        ...(!!taskStartedAt ? { taskStartedAt } : {}),
        ...(!!taskCompletedAt ? { taskCompletedAt } : {}),
      }
    }),
  }
};

// Create the worker with concurrency limit
export const createWorker = (): Worker<JobSimulationNoteData> => {
  const worker = new Worker<JobSimulationNoteData>(
    process.env.JOB_SIMULATION_NOTE_GENERATION_QUEUE_NAME || QUEUE_NAME_DEFAULT,
    async (job: BullJob<JobSimulationNoteData>) => {
      const { progressId, jobId } = job.data;

      try {
        const userProgress = await jobSimulationProgressService.findById(progressId);
        if (!userProgress || userProgress.status !== 'completed') {
          logger.simple(`Note Generation ::: `, progressId, '❌ User progress not found or not completed');
          return false;
        }
        const { jobSimulationId, email } = userProgress;

        logger.simple(`Note Generation ::: `, progressId, 'Starting');

        // Get job simulation
        // Get user progress
        const [jobSimulationGeneration, jobSimulation] = await Promise.all([
          jobSimulationGenerationService.findOne({ jobId }),
          jobSimulationService.findOne({ jobSimulationId }),
        ]);
        if (!jobSimulationGeneration || !jobSimulation || !jobSimulation.isLocal) {
          logger.simple(`Note Generation ::: `, progressId, jobSimulationId, '❌ Job simulation not found or not completed');
          return false;
        }
        // Build JSON Data
        const inputData = buildInputData(jobSimulationGeneration, jobSimulation, userProgress);
        // Create prompt generating note (JSON)
        const systemPrompt = createSystemPrompt();
        const userPrompt = createUserPrompt(JSON.stringify(inputData));
        logger.simple(`Note Generation ::: `, progressId, 'Generating...', new Date().toISOString());
        const result = await generateObject({
          model: deepseek('deepseek-chat'),
          system: systemPrompt,
          prompt: userPrompt,
          schema: EvaluationSchema,
          temperature: 0.5,
        });
        logger.simple(`Note Generation ::: `, progressId, 'Generated', new Date().toISOString());

        await jobSimulationProgressService.findOneAndUpdate(
          { jobSimulationId, email },
          { aiEvaluation: result.object }
        );
        // Save to database (jobsimulationprogresses)

        logger.simple(`Note Generation ::: `, jobSimulationId, email, '✅ Completed');
        return true;

      } catch (error) {
        logger.simple(`Note Generation ::: `, progressId, '❌ Failed', error);
        throw error;
      }
    },
    {
      connection: connectionOptions,
      concurrency: 1,
      removeOnComplete: { count: 2 },
      removeOnFail: { count: 2 }
    }
  );

  // Worker event handlers
  worker.on('completed', (job) => {
    logger.simple(`✅ Batch Worker completed job ${job.id} with data:`, job.returnvalue);
  });

  worker.on('failed', (job, err) => {
    logger.simple(`❌ Batch Worker failed job ${job?.id}:`, err);
  });

  worker.on('error', (err) => {
    logger.simple('❌ Batch Worker error:', err);
  });

  return worker;
};

// Add job to queue
export const addToQueue = async (params: JobSimulationNoteData): Promise<void> => {
  try {
    await jobSimulationNoteQueue.add(
      'process-simulation',
      params,
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: true,
        removeOnFail: false
      }
    );

    logger.simple(`Added to note generation queue: ${params.progressId}`);
  } catch (error) {
    logger.simple(`Failed to add to note generation queue: ${params.progressId}`, error);
    throw error;
  }
};