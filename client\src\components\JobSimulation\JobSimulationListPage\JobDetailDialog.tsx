import { Link } from 'react-router-dom';
import {
  OG<PERSON>ialog,
  OGDialogContent,
  OGDialogFooter,
  OGDialogHeader,
  OGDialogTitle,
} from '~/components';
import { cn } from '~/utils';
import { IListJobPageAgent, IListJobPageJobSimulation } from './types';

const JobDetailDialog = ({
  job,
  agent,
  isOpen,
  setIsOpen,
}: {
  job?: IListJobPageJobSimulation | null;
  agent?: IListJobPageAgent;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}) => {
  return (
    <OGDialog open={isOpen} onOpenChange={setIsOpen}>
      <OGDialogContent className="max-w-md">
        <OGDialogHeader>
          <OGDialogTitle>{job?.name}</OGDialogTitle>
        </OGDialogHeader>

        <div className="flex flex-col gap-4 p-4">
          {job && (
            <>
              <div className="flex items-center gap-4">
                <img
                  src={job.logo}
                  alt={`${job.name} logo`}
                  className="h-16 w-16 object-contain dark:bg-[#2f2f2f]/50"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/assets/logo.png';
                  }}
                />
                <div>
                  <h3 className="text-lg font-semibold">{job.name}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">{job.companyName}</p>
                </div>
              </div>

              <div className="rounded-md bg-gray-50 p-4 dark:bg-gray-700">
                <p className="text-sm">{job.description}</p>
              </div>

              {job.participants !== undefined && (
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Participants:</span>
                  <span className="text-sm">{job.participants.toLocaleString()}</span>
                </div>
              )}

              {agent && (
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Assistant:</span>
                  <div className="flex items-center gap-2">
                    <img
                      src={agent.avatar.filepath}
                      alt={agent.name}
                      className="h-8 w-8 rounded-full object-cover"
                    />
                    <span className="text-sm">{agent.name}</span>
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        <OGDialogFooter>
          <Link
            to={`/job-simulation/${job?.jobSimulationId}`}
            className={cn(
              'flex w-full items-center justify-center rounded-2xl bg-job-primary px-3 py-3 text-center text-sm font-medium text-white',
              'hover:bg-job-primary/90 focus:outline-none focus:ring-2 focus:ring-job-primary focus:ring-offset-2',
            )}
          >
            {job?.progress?.status === 'active' ? 'Resume' : 'Give it a try'}
          </Link>
        </OGDialogFooter>
      </OGDialogContent>
    </OGDialog>
  );
};

export default JobDetailDialog;
