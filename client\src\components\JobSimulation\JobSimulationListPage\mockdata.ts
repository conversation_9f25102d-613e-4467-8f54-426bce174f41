import { IListJobPageAgent, IListJobPageJobSimulation } from "./types";

const sampleJobSimulations: IListJobPageJobSimulation[] = [
    {
        id: '6818de396eae2c750ea99df3',
        jobSimulationId: 'esg-analyst',
        name: 'ESG Analyst',
        description:
            'Work as an ESG Analyst at Greentek Industries, analyzing environmental, social, and governance factors to help companies improve their sustainability practices.',
        logo: '/assets/greentek-logo.png',
        companyName: 'Greentek Industries',
        participants: 123,
        agentId: 'agent_bv2AXZ8dCbC00kOWSj5R2',
    },
    {
        id: '68193c186eae2c750ea99df4',
        jobSimulationId: 'digital-marketing',
        name: 'Digital Marketing Analyst',
        description:
            'Experience the role of a Digital Marketing Specialist at BrightWave Media, creating and implementing marketing strategies for various clients.',
        // logo: 'https://bitcountry-hub-assets.s3.ap-southeast-1.amazonaws.com/images/6801b0712e63bcecf8293e5c/1747191396074__logo.png',
        logo: 'https://bitcountry-hub-assets.s3.ap-southeast-1.amazonaws.com/images/682d9af55a3f459586c280b3/1747923073715__logo.png',
        companyName: 'BrightWave Media',
        participants: 1500,
        agentId: 'agent_-rd8VHlaPeGbDsW5Q3aSq',
    },
];

// Sample data for agents
const sampleAgents: IListJobPageAgent[] = [
    {
        id: 'agent_bv2AXZ8dCbC00kOWSj5R2',
        name: 'Victor Lee',
        avatar: {
            filepath: 'https://beta.bitmeet.io/files/1747796592616-*********-111.jpg',
        },
    },
    {
        id: 'agent_-rd8VHlaPeGbDsW5Q3aSq',
        name: 'Mia',
        avatar: {
            filepath: 'https://d14ciuzrn5ydd5.cloudfront.net/bitmeet/tutor-ai/image-2.jpg',
        },
    },
    {
        id: 'agent_2mnEtGKINAzYtOp27cZAX',
        name: 'James Lane',
        avatar: {
            filepath: 'https://d14ciuzrn5ydd5.cloudfront.net/bitmeet/tutor-ai/image-9.jpg',
        },
    },
];

const getAgents = async (): Promise<IListJobPageAgent[]> => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve(sampleAgents);
        }, 1000);
    });
};

const getJobs = async (): Promise<IListJobPageJobSimulation[]> => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve(sampleJobSimulations);
        }, 1500);
    });
};

export default { getJobs, getAgents, sampleAgents, sampleJobSimulations }