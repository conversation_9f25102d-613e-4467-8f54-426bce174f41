import { Request, Response, Router } from 'express';
import JobSimulationGenerationModel, { IJobSimulationGeneration } from '../models/JobSimulationGeneration';
import { addJobToBatchQueue, addJobToPriorityQueue, jobBatchQueue, jobPriorityQueue } from '../queue/jobQueue';
import { addToQueue as addToNoteGenerationQueue } from '../queue/jobSimulationNoteQueue';
import logger from '../services/logger';

const router = Router();

// Interface for job submission
interface JobSubmission {
  id: string;
  title: string;
  description: string;
  companyName: string;
  companyLogo?: string;
  skills?: string[];
  categories?: string[];
}

// Validation middleware
const validateJobSubmission = (req: Request, res: Response, next: any) => {
  const { jobs } = req.body;

  if (!jobs || !Array.isArray(jobs)) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Jobs array is required'
    });
  }

  if (jobs.length === 0) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Jobs array cannot be empty'
    });
  }

  if (jobs.length > 100) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Maximum 100 jobs allowed per request'
    });
  }

  // Validate each job
  for (let i = 0; i < jobs.length; i++) {
    const job = jobs[i];
    const requiredFields = ['id', 'title', 'description', 'companyName'];

    for (const field of requiredFields) {
      if (!job[field]) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `Job at index ${i} is missing required field: ${field}`
        });
      }
    }

    if (job.skills !== undefined && !Array.isArray(job.skills)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Job at index ${i} has invalid skills field (must be array)`
      });
    }

    if (job.categories !== undefined && !Array.isArray(job.categories)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Job at index ${i} has invalid categories field (must be array)`
      });
    }

    if (typeof job.id !== 'string' || job.id.trim() === '') {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Job at index ${i} has invalid id field`
      });
    }
  }

  next();
};

const validateJobCheck = (req: Request, res: Response, next: any) => {
  const job = req.body || {};
  const jobId = req.params.id;
  const requiredFields = ['id', 'title', 'description', 'companyName'];

  for (const field of requiredFields) {
    if (!job[field]) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Job is missing required field: ${field}`
      });
    }
  }

  if (job.skills !== undefined && !Array.isArray(job.skills)) {
    return res.status(400).json({
      error: 'Bad Request',
      message: `Job has invalid skills field (must be array)`
    });
  }

  if (job.categories !== undefined && !Array.isArray(job.categories)) {
    return res.status(400).json({
      error: 'Bad Request',
      message: `Job has invalid categories field (must be array)`
    });
  }

  if (typeof jobId !== 'string' || job.id.trim() === '') {
    return res.status(400).json({
      error: 'Bad Request',
      message: `Job has invalid id field`
    });
  }

  next();
};

// POST /api/jobs - Submit list jobs for simulation
router.post('/', validateJobSubmission, async (req: Request, res: Response) => {
  try {
    const { jobs }: { jobs: JobSubmission[] } = req.body;

    logger.simple(`Received ${jobs.length} jobs for processing`);

    const responseMetadata = {
      total: jobs.length,
      created: 0,
      updated: 0,
      queued: 0,
      errors: [] as string[]
    };

    const responseJobs = [];

    const [dbJobSimulationGenerations, waitingJobsInQueue] = await Promise.all([
      JobSimulationGenerationModel.find({ jobId: { $in: jobs.map(job => job.id) } }),
      jobBatchQueue.getWaiting()
    ]);
    if (waitingJobsInQueue.length > parseInt(process.env.MAX_JOBS_IN_BATCH_QUEUE || '20')) {
      throw new Error('Too many jobs in queue, please wait for some to complete');
    }
    const mapJobSimulationGenerationById = dbJobSimulationGenerations.reduce((acc, curr) => {
      acc[curr.jobId] = curr;
      return acc;
    }, {} as Record<string, IJobSimulationGeneration>);

    for (const jobData of jobs) {
      try {
        // Check if job already exists
        const existingJob = mapJobSimulationGenerationById[jobData.id];

        if (existingJob) {
          const { id: jobId, ...restJobData } = jobData;

          if (existingJob.simulationStatus === 'pending' || existingJob.simulationStatus === 'failed') {
            await JobSimulationGenerationModel.updateOne(
              { jobId },
              {
                ...restJobData,
                simulationStatus: 'pending',
                webhookStatus: 'pending',
                webhookRetryCount: 0,
                errorMessage: null,
                updatedAt: new Date()
              }
            );
            responseJobs.push({
              jobId,
              simulationStatus: 'pending'
            });

            // Add to queue
            await addJobToBatchQueue(jobData.id);
            responseMetadata.updated++;
            responseMetadata.queued++;
          } else {
            logger.simple(`⚠️ Job ${jobData.id} already exists and is being processed or completed`);
            responseJobs.push({
              jobId,
              simulationStatus: existingJob.simulationStatus === "processing" ? "pending" : existingJob.simulationStatus,
              ...(existingJob.simulationStatus === 'completed' ? {
                id: existingJob.simulationData?.jobSimulationId,
                level: existingJob.simulationData?.level,
                minute: existingJob.simulationData?.minutes,
              } : {})
            });
          }
        } else {
          // Create new job
          const { id: jobId, ...restJobData } = jobData;
          const newJob = new JobSimulationGenerationModel({
            ...restJobData,
            jobId,
            simulationStatus: 'pending',
            webhookStatus: 'pending',
            webhookRetryCount: 0
          });

          await newJob.save();
          responseJobs.push({
            jobId,
            simulationStatus: 'pending'
          });

          // Add to queue
          await addJobToBatchQueue(jobData.id);
          responseMetadata.created++;
          responseMetadata.queued++;
        }

      } catch (error) {
        const errorMessage = `Failed to process job ${jobData.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        logger.simple(`❌ ${errorMessage}`);
        responseMetadata.errors.push(errorMessage);
      }
    }

    logger.simple(`✅ Job submission completed:`, responseMetadata);

    res.status(200).json({
      message: 'Jobs submitted successfully',
      data: {
        simulations: responseJobs,
        metadata: responseMetadata
      }
    });

  } catch (error: any) {
    logger.simple('Error processing job submission:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error?.message || 'Failed to process job submission'
    });
  }
});

// GET /api/jobs - Get job status
router.get('/', async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 20, status, webhookStatus } = req.query;

    const filter: any = {};
    if (status) filter.simulationStatus = status;
    if (webhookStatus) filter.webhookStatus = webhookStatus;

    const skip = (Number(page) - 1) * Number(limit);

    const jobs = await JobSimulationGenerationModel.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit))
      .select('-simulationData'); // Exclude large simulation data from list

    const total = await JobSimulationGenerationModel.countDocuments(filter);

    res.status(200).json({
      jobs,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });

  } catch (error) {
    logger.simple('❌ Error fetching jobs:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch jobs'
    });
  }
});

router.post('/:id', validateJobCheck, async (req: Request, res: Response) => {
  try {
    const jobId = req.params.id;
    logger.simple(`Received ${jobId} jobs for processing`);


    const [existingJob, waitingPriorityJobsInQueue] = await Promise.all([
      JobSimulationGenerationModel.findOne({ jobId }),
      jobPriorityQueue.getWaiting()
    ]);

    if (waitingPriorityJobsInQueue.length >= parseInt(process.env.MAX_JOBS_IN_PRIORITY_QUEUE || '6')) {
      throw new Error('Too many jobs in priority queue, please wait for some to complete');
    }

    let responseData = {};

    // Check if job already exists
    if (existingJob) {
      const { id, ...restJobData } = req.body;
      logger.simple(`⚠️ Job ${jobId} is`, existingJob.simulationStatus);
      // Update existing job if not completed
      if (existingJob.simulationStatus === 'pending' || existingJob.simulationStatus === 'failed') {
        await JobSimulationGenerationModel.updateOne(
          { jobId },
          {
            ...restJobData,
            simulationStatus: 'pending',
            webhookStatus: 'pending',
            webhookRetryCount: 0,
            errorMessage: null,
            updatedAt: new Date()
          }
        );
        await addJobToPriorityQueue(jobId);
        responseData = {
          jobId,
          simulationStatus: 'failed'
        }
      } else {
        responseData = {
          jobId,
          simulationStatus: existingJob.simulationStatus === "processing" ? "failed" : existingJob.simulationStatus,
          ...(existingJob.simulationStatus === 'completed' ? {
            id: existingJob.simulationData?.jobSimulationId,
            level: existingJob.simulationData?.level,
            minute: existingJob.simulationData?.minutes,
          } : {})
        };
      }
    } else {
      // Create new job
      const { id, ...restJobData } = req.body;
      const newJob = new JobSimulationGenerationModel({
        ...restJobData,
        jobId,
        simulationStatus: 'pending',
        webhookStatus: 'pending',
        webhookRetryCount: 0
      });

      await newJob.save();
      await addJobToPriorityQueue(jobId);
      responseData = {
        jobId,
        simulationStatus: 'failed'
      };
    }

    res.status(200).json(responseData);

  } catch (error) {
    logger.simple('Error processing job submission:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to process job submission'
    });
  }
});

// GET /api/jobs/:id - Get specific job details
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const job = await JobSimulationGenerationModel.findOne({ jobId: id });

    if (!job) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Job with id ${id} not found`
      });
    }

    res.status(200).json({ job });

  } catch (error) {
    logger.simple('❌ Error fetching job:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch job'
    });
  }
});

router.post('/:id/evaluate', async (req: Request, res: Response) => {
  try {
    const progressId = req.params.id;
    const jobId = req.body.jobId;

    if (!progressId || !jobId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Missing required progressId | jobId'
      });
    }

    await addToNoteGenerationQueue({ progressId, jobId });

    const d = new Date();

    res.status(200).json({
      message: 'Job evaluation requested',
      data: {
        progressId,
        time: d.toTimeString(),
        timestamp: d.getTime(),
      }
    });

  } catch (error) {
    logger.simple('Error processing job submission:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to process job submission'
    });
  }
});

export default router;
