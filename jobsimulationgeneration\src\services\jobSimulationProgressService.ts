import { PipelineStage } from 'mongoose';
import Model from '../models/JobSimulationProgress';

const findOne = async (filter: Record<string, any>) => {
    return await Model.findOne(filter);
};

const findById = async (id: string) => {
    return await Model.findById(id);
};

const findOneAndUpdate = async (filter: Record<string, any>, update: Record<string, any>, options?: Record<string, any>) => {
    return await Model.findOneAndUpdate(filter, update, options);
};

const countDocuments = async (filter?: Record<string, any>) => {
    return await Model.countDocuments(filter);
};

const aggregate = async (pipeline: PipelineStage[]) => {
    return await Model.aggregate(pipeline);
};

export default {
    findOne,
    findById,
    findOneAndUpdate,
    countDocuments,
    aggregate,
}