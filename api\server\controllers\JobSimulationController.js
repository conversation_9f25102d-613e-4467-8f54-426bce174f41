const JobSimulationService = require('~/server/services/JobSimulation/JobSimulationService');

const JobSimulationController = {

  async getUserJobSimulationsInfo(req, res) {
    try {
      const params = {
        search: req.query.search || "",
        page: +(req.query.page || 1),
        limit: +(req.query.limit || 10),
        email: req.user.email,
      };
      const data = await JobSimulationService.getUserJobSimulationsInfo(params);
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to get list of job simulation' });
    }
  },

  async getUserJobSimulationInfo(req, res) {
    try {
      // req.params.jobSimulationId
      const params = {
        jobSimulationId: req.params.jobSimulationId,
        email: req.user.email,
      };
      const data = await JobSimulationService.getUserJobSimulationInfo(params);
      res.json({
        jobSimulation: data,
        user: {
          email: req.user.email,
          name: req.user.name,
          id: req.user.id,
        }
      });
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to get list of job simulation' });
    }
  },

  async getAdminJobSimulations(req, res) {
    try {
      const params = {
        search: req.query.search || "",
        page: +(req.query.page || 1),
        limit: +(req.query.limit || 6),
      };
      const data = await JobSimulationService.getAdminJobSimulations(params);
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to get list of job simulation' });
    }
  },

  async getAdminJobSimulation(req, res) {
    try {
      const data = await JobSimulationService.getAdminJobSimulation(req.params.jobSimulationId);
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to get job simulation' });
    }
  },

  async updateLogo(req, res) {
    try {
      // TODO: check role admin, ...
      const data = await JobSimulationService.updateLogo(req.params.jobSimulationId, req, req.file);
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to update job simulation logo' });
    }
  },

  async updateCredentials(req, res) {
    try {
      if (!req.body.username || !req.body.password) {
        throw new Error('Username and password are required');
      }
      const data = await JobSimulationService.updateCredentials(req.params.jobSimulationId, req.body.username, req.body.password);
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to update job simulation credentials' });
    }
  },

  async getProgress(req, res) {
    try {
      const data = await JobSimulationService.getProgress(req.params.jobSimulationId, req.user.email);
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to get progress' });
    }
  },

  async getOrCreateProgress(req, res) {
    try {
      const data = await JobSimulationService.getOrCreateProgress({
        jobSimulationId: req.params.jobSimulationId,
        email: req.user.email,
        userId: req.user.id,
      });
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to get or create progress' });
    }
  },

  async updateProgress(req, res) {
    try {
      const data = await JobSimulationService.updateProgress(req.params.jobSimulationId, req.user.email, req.body || {});
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to update progress' });
    }
  },
};

module.exports = JobSimulationController;
