import type {
  TJobSimulationProgressTaskSubmission,
  TJobSimulationTask,
} from 'librechat-data-provider';
import { ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { TJobSimulationContext } from '~/common';
import {
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui';

import MarkdownEmail from './Email/templates/MarkdownEmail';

import { useGetJobSimulationDataQuery } from '~/data-provider/JobSimulation';
import useGetEmailTask from '~/hooks/CustomActions/useGetEmailTask';
import { cn } from '~/utils';

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const getStatusStyles = (status: string) => {
    switch (status) {
      case 'passed':
        return 'bg-green-500 text-white';
      case 'failed':
        return 'bg-red-500 text-white';
      default: // 'todo'
        return 'bg-white text-black border border-gray-300';
    }
  };

  return (
    <span
      className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${getStatusStyles(status)}`}
    >
      {status}
    </span>
  );
};

// Task detail component
const TaskDetail = ({
  task,
  enabled,
  submissions,
  onBack,
}: {
  task: TJobSimulationTask;
  enabled: boolean;
  submissions: TJobSimulationProgressTaskSubmission[];
  onBack: () => void;
}) => {
  const { getEmailTask } = useGetEmailTask();
  const [expandedSubmissions, setExpandedSubmissions] = useState<string[]>([]);

  const sortedSubmissions = useMemo(() => {
    return [...submissions].sort((a, b) => {
      const dateA = new Date(a.submittedAt).getTime();
      const dateB = new Date(b.submittedAt).getTime();
      return dateB - dateA; // Sort by newest first
    });
  }, [submissions]);

  const toggleSubmission = (index: number) => {
    const submissionId = `submission-${index}`;
    setExpandedSubmissions((prev) =>
      prev.includes(submissionId)
        ? prev.filter((id) => id !== submissionId)
        : [...prev, submissionId],
    );
  };

  const handleRequestTask = () => {
    if (!enabled) return;
    getEmailTask();
  };

  return (
    <div className="flex h-full flex-col p-5">
      {/* Header with back button */}
      <div className="mb-6 flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={onBack} className="flex items-center gap-2">
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-xl font-semibold">{task.title}</h1>
      </div>

      {/* Task content */}
      <div className="flex-1">
        <div className="prose !w-full !max-w-full rounded-lg bg-gray-50 p-5">
          {task.description ? (
            <MarkdownEmail content={task.description} />
          ) : (
            <p className="text-gray-500">No description available</p>
          )}
        </div>

        <div className="mt-5 text-right">
          <Button variant="default" size="sm" onClick={handleRequestTask} disabled={!enabled}>
            Request Task
          </Button>
        </div>

        {/* Submissions */}
        <div className="my-7">
          <hr />
        </div>
        <div className="pb-3">
          <div>
            <div className="font-bold">Submissions ({sortedSubmissions.length})</div>
          </div>
          <div className="mt-2">
            {sortedSubmissions.length === 0 ? (
              <p className="text-gray-500">No submissions yet</p>
            ) : (
              <div className="space-y-4">
                {sortedSubmissions.map((submission, index) => (
                  <div key={index} className="rounded-lg border">
                    <button
                      onClick={() => toggleSubmission(index)}
                      className="flex w-full items-center justify-between p-4 text-left hover:bg-gray-50"
                    >
                      <div className="flex items-center gap-3">
                        <span className="font-medium">
                          Submission {sortedSubmissions.length - index}
                        </span>
                        <StatusBadge status={submission.status || 'todo'} />
                        <span className="text-sm text-gray-500">
                          {new Date(submission.submittedAt).toLocaleString()}
                        </span>
                      </div>
                      {expandedSubmissions.includes(`submission-${index}`) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </button>

                    {expandedSubmissions.includes(`submission-${index}`) && (
                      <div className="space-y-4 border-t p-4">
                        <div>
                          <h4 className="mb-2 font-medium">Content:</h4>
                          <div className="prose !w-full !max-w-full rounded bg-gray-50 p-3">
                            <MarkdownEmail content={submission.content} />
                          </div>
                        </div>

                        {submission.feedback && (
                          <div>
                            <h4 className="mb-2 font-medium">Feedback:</h4>
                            <div className="prose !w-full !max-w-full rounded bg-blue-50 p-3">
                              <MarkdownEmail content={submission.feedback} />
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Main LocalTaskBoard component
export const LocalTaskBoard = () => {
  const { jobSimulationData: currentData } = useOutletContext<TJobSimulationContext>();
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);

  const { data: jobSimulationData, refetch } = useGetJobSimulationDataQuery(
    currentData?.jobSimulationId || '',
    'LocalTaskBoard',
    { enabled: false },
  );

  useEffect(() => {
    console.log('LocalTaskBoard ::: refetch job data');
    refetch();
  }, []);

  // Merge tasks data with progress data
  const tasksWithStatus = useMemo(() => {
    if (!jobSimulationData?.tasks) return [];

    return jobSimulationData.tasks
      .map((task, index) => {
        // Find progress task to get status
        const progressTask = jobSimulationData?.progress?.tasks?.find((pt) => pt.id === task.id);
        const previousTask = index > 0 ? jobSimulationData?.progress?.tasks?.[index - 1] : null;
        return {
          ...task,
          status: progressTask?.status || 'todo',
          enabled: index === 0 || previousTask?.status === 'passed',
        };
      })
      .sort((a, b) => (a.order || 0) - (b.order || 0)); // Sort by order
  }, [jobSimulationData]);

  // Get selected task details
  const selectedTask = useMemo(() => {
    if (!selectedTaskId) return null;
    return jobSimulationData?.tasks?.find((task) => task.id === selectedTaskId);
  }, [selectedTaskId, jobSimulationData]);

  // Get submissions for selected task
  const selectedTaskSubmissions = useMemo(() => {
    if (!selectedTaskId) return [];
    const progressTask = jobSimulationData?.progress?.tasks?.find((pt) => pt.id === selectedTaskId);
    return progressTask?.submissions || [];
  }, [selectedTaskId, jobSimulationData]);

  const handleTaskClick = (taskId: string) => {
    setSelectedTaskId(taskId);
  };

  const handleBackToList = () => {
    setSelectedTaskId(null);
  };

  // Show task detail if a task is selected
  if (selectedTaskId && selectedTask) {
    return (
      <TaskDetail
        task={selectedTask}
        enabled={
          !!tasksWithStatus.find((task) => task.id === selectedTaskId)?.enabled &&
          tasksWithStatus.find((task) => task.id === selectedTaskId)?.status !== 'passed'
        }
        submissions={selectedTaskSubmissions}
        onBack={handleBackToList}
      />
    );
  }

  // Show tasks list
  return (
    <div className="relative h-full p-5">
      <div className="overflow-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-16">No.</TableHead>
              <TableHead>TASK</TableHead>
              <TableHead className="w-24">Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tasksWithStatus.length === 0 ? (
              <TableRow>
                <TableCell colSpan={3} className="py-8 text-center text-gray-500">
                  No tasks available
                </TableCell>
              </TableRow>
            ) : (
              tasksWithStatus.map((task, index) => (
                <TableRow
                  key={task.id}
                  className={cn('cursor-pointer hover:bg-gray-50')}
                  onClick={() => handleTaskClick(task.id)}
                >
                  <TableCell
                    className={cn('font-medium', !(task as any).enabled && 'text-gray-300')}
                  >
                    {index + 1}
                  </TableCell>
                  <TableCell>
                    <div className={cn('font-medium', !(task as any).enabled && 'text-gray-300')}>
                      {task.title}
                    </div>
                    {/* {task.description && (
                      <div className="max-w-md truncate text-sm text-gray-500">
                        {task.description.replace(/[#*`]/g, '').substring(0, 100)}...
                      </div>
                    )} */}
                  </TableCell>
                  <TableCell>
                    <StatusBadge status={task.status} />
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default LocalTaskBoard;
