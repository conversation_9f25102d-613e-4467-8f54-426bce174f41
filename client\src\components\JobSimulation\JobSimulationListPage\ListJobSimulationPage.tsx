import { easings } from '@react-spring/web';
import { memo, useEffect, useState } from 'react';
import { SplitText, Spinner } from '~/components';
import { useUserJobSimulationsInfo } from '~/data-provider/JobSimulation/queries';
import { useAuthContext } from '~/hooks';
import CertificationDialog from './CertificationDialog';
import JobCard from './JobCard';
import JobDetailDialog from './JobDetailDialog';
import MockData from './mockdata';
import { IListJobPageAgent, IListJobPageJobSimulation } from './types';
import UserProfileDropdown from './UserProfileDropdown';

const ListJobSimulationPage = () => {
  const { isAuthenticated } = useAuthContext();
  // const [jobs, setJobs] = useState<JobSimulation[]>([]);
  const [agents, setAgents] = useState<IListJobPageAgent[]>([]);
  // const [isLoading, setIsLoading] = useState(true);
  const [selectedJob, setSelectedJob] = useState<IListJobPageJobSimulation | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isOpenCertificationDialog, setIsOpenCertificationDialog] = useState(false);
  const [certificationData, setCertificationData] = useState<{
    intakeId: string;
    email: string;
    jobSimulationId: string;
  } | null>({
    intakeId: '',
    email: '',
    jobSimulationId: '',
  });

  const { data = [], isLoading: isLoadingData } = useUserJobSimulationsInfo(
    { page: 1, limit: 6 },
    { enabled: isAuthenticated },
  );
  const jobs: IListJobPageJobSimulation[] = data;
  // console.log('useUserJobSimulationsInfo ::: data: ', jobs);

  const handleViewDetails = (job: IListJobPageJobSimulation) => {
    setSelectedJob(job);
    setIsDialogOpen(true);
  };

  const handleViewCertification = (job: IListJobPageJobSimulation) => {
    if (!job.progress?.intakeId || !job.progress?.email) return;
    setCertificationData({
      intakeId: job.progress.intakeId,
      email: job.progress.email,
      jobSimulationId: job.jobSimulationId,
    });
    setIsOpenCertificationDialog(true);
  };

  const handleCloseCertification = () => {
    setIsOpenCertificationDialog(false);
    setCertificationData(null);
  };

  const findAgentForJob = (job: IListJobPageJobSimulation | null) => {
    return agents.find((agent) => agent.id === job?.agentId);
  };

  // Fetch jobs and agents
  useEffect(() => {
    const fetchData = async () => {
      // setIsLoading(true);
      try {
        // const [jobsData, agentsData] = await Promise.all([getJobs(), getAgents()]);
        const agentsData = await MockData.getAgents();

        // setJobs(jobsData);
        setAgents(agentsData);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        // setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="relative flex h-auto w-full flex-col px-4 py-8 lg:min-h-screen">
      {/* Image at the top, centered */}
      <div className="mb-9 flex w-full justify-center">
        <img
          src="/assets/list-job-logo.png"
          alt="list job"
          className="h-auto max-h-52 max-w-full"
        />
      </div>

      <h1 className="mb-10 text-center text-3xl font-bold dark:text-white">
        {isLoadingData ? (
          // 'Getting jobs ready...'
          <SplitText
            key={`split-text-getting-ready`}
            text="Getting jobs ready..."
            className="text-3xl font-medium text-text-primary"
            delay={50}
            textAlign="center"
            animationFrom={{ opacity: 0, transform: 'translate3d(0,50px,0)' }}
            animationTo={{ opacity: 1, transform: 'translate3d(0,0,0)' }}
            easing={easings.easeOutCubic}
            threshold={0}
            rootMargin="0px"
          />
        ) : (
          <SplitText
            key={`split-text-select-job`}
            text="Select a job you want to experience."
            className="text-3xl font-medium text-text-primary"
            delay={50}
            textAlign="center"
            animationFrom={{ opacity: 0, transform: 'translate3d(0,50px,0)' }}
            animationTo={{ opacity: 1, transform: 'translate3d(0,0,0)' }}
            easing={easings.easeOutCubic}
            threshold={0}
            rootMargin="0px"
          />
        )}
      </h1>

      {isLoadingData ? (
        <div className="flex h-40 w-full items-center justify-center">
          <Spinner className="h-10 w-10 text-blue-600" />
        </div>
      ) : (
        <div className="flex w-full justify-center">
          <div className="mx-auto grid w-full max-w-6xl grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {jobs.length === 1 ? (
              <div className="col-span-full flex w-full justify-center md:mx-auto md:max-w-md">
                <JobCard
                  key={jobs[0].id}
                  job={jobs[0]}
                  agent={findAgentForJob(jobs[0])}
                  onViewDetails={handleViewDetails}
                  onViewCertification={handleViewCertification}
                />
              </div>
            ) : jobs.length === 2 ? (
              <div className="col-span-full grid w-full grid-cols-1 gap-6 sm:grid-cols-2 md:mx-auto md:max-w-3xl">
                {jobs.slice(0, 2).map((job) => (
                  <JobCard
                    key={job.id}
                    job={job}
                    agent={findAgentForJob(job)}
                    onViewDetails={handleViewDetails}
                    onViewCertification={handleViewCertification}
                  />
                ))}
              </div>
            ) : jobs.length <= 6 ? (
              <div className="col-span-full grid w-full grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                {jobs.slice(0, 6).map((job) => (
                  <JobCard
                    key={job.id}
                    job={job}
                    agent={findAgentForJob(job)}
                    onViewDetails={handleViewDetails}
                    onViewCertification={handleViewCertification}
                  />
                ))}
              </div>
            ) : (
              <>
                {jobs.slice(0, 6).map((job) => (
                  <JobCard
                    key={job.id}
                    job={job}
                    agent={findAgentForJob(job)}
                    onViewDetails={handleViewDetails}
                    onViewCertification={handleViewCertification}
                  />
                ))}
              </>
            )}
          </div>
        </div>
      )}

      {/* Job details dialog */}
      <JobDetailDialog
        isOpen={isDialogOpen}
        setIsOpen={setIsDialogOpen}
        job={selectedJob}
        agent={findAgentForJob(selectedJob)}
      />
      <CertificationDialog
        isOpen={isOpenCertificationDialog}
        setIsOpen={handleCloseCertification}
        intakeId={certificationData?.intakeId || ''}
        email={certificationData?.email || ''}
        jobSimulationId={certificationData?.jobSimulationId || ''}
      />

      <div className="fixed bottom-0 left-0 md:m-4">
        <UserProfileDropdown />
      </div>
    </div>
  );
};

export default memo(ListJobSimulationPage);
