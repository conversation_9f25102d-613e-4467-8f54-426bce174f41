import { PipelineStage } from 'mongoose';
import JobSimulationGenerationModel from '../models/JobSimulationGeneration';

const findOne = async (filter?: Record<string, any>) => {
    return await JobSimulationGenerationModel.findOne(filter);
};

const findOneAndUpdate = async (filter: Record<string, any>, update: Record<string, any>, options?: Record<string, any>) => {
    return await JobSimulationGenerationModel.findOneAndUpdate(filter, update, options);
};

const countDocuments = async (filter?: Record<string, any>) => {
    return await JobSimulationGenerationModel.countDocuments(filter);
};

const aggregate = async (pipeline: PipelineStage[]) => {
    return await JobSimulationGenerationModel.aggregate(pipeline);
};

export default {
    findOne,
    findOneAndUpdate,
    countDocuments,
    aggregate,
}