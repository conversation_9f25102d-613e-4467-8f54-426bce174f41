const express = require('express');
const router = express.Router();
const JobSimulationController = require('~/server/controllers/JobSimulationController');
const JobSimulationTaskController = require('~/server/controllers/JobSimulationTaskController');
const { requireJwtAuth } = require('~/server/middleware');

router.use(requireJwtAuth);

router.post('/:jobSimulationId', JobSimulationController.getOrCreateProgress);
router.put('/:jobSimulationId/update', JobSimulationController.updateProgress);
// router.put('/:jobSimulationId/update-tasks', JobSimulationTaskController.updateProgressTasks);

module.exports = router;
