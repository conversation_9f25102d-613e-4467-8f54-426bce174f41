import mongoose, { Document, Schema } from 'mongoose';
import { IJobSimulationTask } from './JobSimulation';

export interface IJobSimulationProgress extends Document {
  jobSimulationId: string;
  email: string;
  status: 'completed' | 'active';
  tasks: IJobSimulationTask[];
  emails: { id: string, type: string, time: number, [key: string]: any }[];
  createdAt: Date;
  completedAt?: Date;
  completionMins?: number;
}

const JobSimulationProgressSchema: Schema = new Schema({
  jobSimulationId: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
  },
  tasks: {
    type: Schema.Types.Mixed,
    required: true,
    default: [],
  },
  status: {
    type: String,
    required: true,
    default: 'active',
  },
}, {
  timestamps: true,
  strict: false,
  autoIndex: false
});

export default mongoose.model<IJobSimulationProgress>('jobSimulationProgress', JobSimulationProgressSchema);
