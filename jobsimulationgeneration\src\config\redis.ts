import { Redis } from 'ioredis';
import logger from '../services/logger';

export const createRedisConnection = (): Redis => {
  const redis = new Redis(process.env.REDIS_URI!);

  redis.on('connect', () => {
    logger.simple('✅ Redis connected successfully at DB', process.env.REDIS_DB);
  });

  redis.on('reconnecting', () => {
    logger.simple('🔄 Redis reconnecting...');
  });

  redis.on('error', (error: any) => {
    logger.simple('❌ Redis connection error:', error);
  });

  redis.on('close', () => {
    logger.simple('⚠️ Redis connection closed');
  });

  return redis;
};

export const connectionOptions = {
  host: process.env.REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT || '6349'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '2'),
}
