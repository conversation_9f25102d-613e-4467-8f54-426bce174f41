import { TJobSimulationEmail } from 'src/types';
import cloudArchitectEmailService from './mock-email-cloud-architect';
import defaultEmailService from './mock-email-default-job';
import getBrightwaveEmails from './mock-email-digital-marketing-analyst';
import getESGAnalystEmails from './mock-email-esg-analyst';
import mobileTestingEmailService from './mock-email-mobile-testing';
import templateEmailService from './templates';

export const getJobSimulationEmails = (data: {
  jobSimulation: any;
  user: any,
  // jobSimulationId: string;
  // logo: string;
  // billionIntakeId?: string;
  // userName: string;
  // [key: string]: any;
}): TJobSimulationEmail[] => {
  try {
    const { jobSimulation, user } = data;
    if (['esg-analyst'].includes(jobSimulation.jobSimulationId)) {
      return getESGAnalystEmails.getEmails(jobSimulation);
    }
    if (['digital-marketing'].includes(jobSimulation.jobSimulationId)) {
      return getBrightwaveEmails.getEmails(jobSimulation);
    }
    if (['mobile-testing'].includes(jobSimulation.jobSimulationId)) {
      return mobileTestingEmailService.getEmails(jobSimulation);
    }
    if (['cloud-architect'].includes(jobSimulation.jobSimulationId)) {
      return cloudArchitectEmailService.getEmails(jobSimulation);
    }

    if (!jobSimulation.billionIntakeId && jobSimulation.isLocal) {
      return templateEmailService.getEmails({ jobSimulation, userName: user.name });
    }

    return defaultEmailService.getEmails(jobSimulation);
  } catch (error) {
    console.log('Error get emails ::: ', error);
    return [];
  }
};

export const getJobSimulationEmailTask = (data: { jobSimulation: any; task: any; user: any }) => {
  try {
    const jobSimulationId = data.jobSimulation.jobSimulationId;
    if (['esg-analyst'].includes(jobSimulationId)) {
      return getESGAnalystEmails.buildEmailTask(data);
    }
    if (['digital-marketing'].includes(jobSimulationId)) {
      return getBrightwaveEmails.buildEmailTask(data);
    }
    if (['mobile-testing'].includes(jobSimulationId)) {
      return mobileTestingEmailService.buildEmailTask(data);
    }
    if (['cloud-architect'].includes(jobSimulationId)) {
      return cloudArchitectEmailService.buildEmailTask(data);
    }
    if (data.jobSimulation.isLocal) {
      return templateEmailService.buildEmailTask({ jobSimulation: data.jobSimulation, task: data.task, userName: data.user.name });
    }
    // TODO: build local task
    return defaultEmailService.buildEmailTask(data);
  } catch (error) {
    return null;
  }
};
